<template>
  <div class="timer-container">
    <div class="timer-wrapper">
      <div class="controls-section">
        <div class="header-row">
          <div class="session-title">
            {{ timerStore.activeSession?.session_name || 'Pomodoro Timer' }}
          </div>
          <div class="settings-icon-container">
            <img :src="settingsIcon" class="settings-icon" alt="Settings" @click="showSettings = true" />
          </div>
        </div>

        <div class="button-group">
          <div class="timer-button"
            :class="{ 'active': timerStore.timerType === 'pomodoro', 'outlined': timerStore.timerType !== 'pomodoro' }"
            @click="handleSwitchTimerType('pomodoro')">
            Pomodoro
          </div>
          <div class="timer-button"
            :class="{ 'active': timerStore.timerType === 'shortBreak', 'outlined': timerStore.timerType !== 'shortBreak' }"
            @click="handleSwitchTimerType('shortBreak')">
            Short Break
          </div>
          <div class="timer-button"
            :class="{ 'active': timerStore.timerType === 'longBreak', 'outlined': timerStore.timerType !== 'longBreak' }"
            @click="handleSwitchTimerType('longBreak')">
            Long Break
          </div>
        </div>

        <!-- Settings Modal -->
        <TimerSettingsModal v-if="showSettings" @close="showSettings = false" @update-settings="handleUpdateSettings"
          :pomodoroTime="timerStore.pomodoroTime" :shortBreakTime="timerStore.shortBreakTime"
          :longBreakTime="timerStore.longBreakTime" :longBreakInterval="timerStore.longBreakInterval"
          :autoStartBreaks="timerStore.autoStartBreaks" :autoStartPomodoros="timerStore.autoStartPomodoros" />
      </div>

      <div class="time-display">
        <div class="time-minutes">{{ timerStore.formattedMinutes }}</div>
        <div class="time-separator">:</div>
        <div class="time-seconds">{{ timerStore.formattedSeconds }}</div>
      </div>

      <div class="action-controls">
        <img :src="restartIcon" class="restart-button" alt="Reset Timer"
          @click="handleRestartTimer" />
        <!-- FIX: Updated play button logic to allow break pause/resume -->
        <img :src="timerStore.isRunning ? pauseIcon : playIcon" class="play-button"
          :class="{ 'disabled': shouldDisablePlayButton }" :alt="timerStore.isRunning ? 'Pause' : 'Play'"
          @click="handleToggleTimer" />
        <img :src="skipIcon" class="skip-button" alt="Skip" @click="handleSkipTimer" />
      </div>

      <div class="divider"></div>

      <div class="stats-section">
        <div class="stat-text pomodoro-counter-text">Pomodoro Counter: {{ timerStore.pomodoroCount }}</div>
        <div class="end-session-container" v-show="timerStore.sessionActive">
          <button class="end-session-button" @click="handleEndSession">
            End Session
          </button>
        </div>
        <div class="stat-text focus-time-text">Focus Time: {{ timerStore.formattedFocusTime }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, onBeforeUnmount, computed } from 'vue';
import TimerSettingsModal from './TimerSettingsModal.vue';
import { useTimerStore } from '../../stores/timerStore';

// Import icons as modules for proper path resolution in production
import settingsIcon from '/icons/settings-icon.svg'
import restartIcon from '/icons/restart-icon.svg'
import playIcon from '/icons/play-icon.svg'
import pauseIcon from '/icons/pause-icon.svg'
import skipIcon from '/icons/skip-icon.svg'

export default defineComponent({
  components: {
    TimerSettingsModal
  },
  name: 'PomodoroTimer',
  props: {
    sessionActive: {
      type: Boolean,
      default: false
    },
    sessionInfo: {
      type: Object as () => Record<string, any> | null,
      default: null
    }
  },
  emits: ['end-session', 'session-completed', 'session-auto-created', 'session-updated'],
  setup(props, { emit }) {
    console.log('🍅 [PomodoroTimer] Component setup started')

    const timerStore = useTimerStore()

    // Local component state
    const showSettings = ref(false)

    // FIX: Throttle session updates to prevent spam
    let lastSessionUpdateTime = 0
    const SESSION_UPDATE_THROTTLE = 5000 // 5 seconds instead of every second

    // FIX: Updated play button logic
    const shouldDisablePlayButton = computed(() => {
      // Only disable if:
      // 1. Timer is NOT currently running AND
      // 2. We're in a break period AND
      // 3. There's no active session to resume
      return !timerStore.isRunning &&
        !timerStore.canStartSession &&
        !timerStore.activeSession
    })



    console.log('🍅 [PomodoroTimer] Initial timer store state:', {
      isRunning: timerStore.isRunning,
      timerType: timerStore.timerType,
      timeLeft: timerStore.timeLeft,
      sessionActive: timerStore.sessionActive,
      activeSession: timerStore.activeSession?.id
    })

    // Timer control handlers
    const handleSwitchTimerType = (type: 'pomodoro' | 'shortBreak' | 'longBreak') => {
      console.log('🍅 [PomodoroTimer] handleSwitchTimerType called:', type)
      timerStore.switchTimerType(type)
    }

    const handleToggleTimer = async () => {
      console.log('🍅 [PomodoroTimer] handleToggleTimer called:', {
        currentlyRunning: timerStore.isRunning,
        canStartSession: timerStore.canStartSession,
        hasActiveSession: !!timerStore.activeSession,
        shouldDisable: shouldDisablePlayButton.value
      })

      if (shouldDisablePlayButton.value) {
        console.log('🍅 [PomodoroTimer] Play button is disabled, ignoring click')
        return
      }

      const result = await timerStore.toggleTimer()

      if (!result.success) {
        console.log('🍅 [PomodoroTimer] Toggle failed:', result.reason)
        return
      }

      console.log('🍅 [PomodoroTimer] Toggle successful, new state:', timerStore.isRunning)

      // Show session start notification if we just started a timer
      if (timerStore.isRunning && timerStore.activeSession?.session_name) {
        console.log('🍅 [PomodoroTimer] Showing start notification for:', timerStore.activeSession.session_name)
      }
    }

    // FIX: Improved skip timer logic
    const handleSkipTimer = async () => {
      console.log('🍅 [PomodoroTimer] handleSkipTimer called')

      // Check current state before skipping
      const currentType = timerStore.timerType
      const wasRunning = timerStore.isRunning
      const oldCount = timerStore.pomodoroCount

      // IMPORTANT: Check if the timer was ever started for this cycle
      // This should come from the timer store - we need to track if timer was started
      const wasEverStarted = timerStore.wasTimerStartedThisCycle // This property needs to exist

      console.log('🍅 [PomodoroTimer] Skip details:', {
        currentType,
        wasRunning,
        wasEverStarted, // NEW: Track if timer was ever started
        oldCount,
        hasActiveSession: !!timerStore.activeSession,
        currentCycleId: timerStore.state.currentCycleId
      })

      // Only create session if we're skipping a pomodoro that was actually started
      if (currentType === 'pomodoro' && !timerStore.activeSession && wasEverStarted) {
        console.log('⚠️ [PomodoroTimer] No active session for started pomodoro skip, creating one...')
      }

      try {
        // Call skipTimer - the store already tracks if timer was started internally
        await timerStore.skipTimer()

        console.log('🍅 [PomodoroTimer] Skip completed, new state:', {
          newType: timerStore.timerType,
          newCount: timerStore.pomodoroCount,
          totalFocusTime: timerStore.totalFocusTime,
          isRunning: timerStore.isRunning
        })

        // Handle different skip scenarios
        if (currentType === 'pomodoro' && wasEverStarted) {
          // Only emit completion events if the timer was actually started
          console.log('🍅 [PomodoroTimer] Started pomodoro was skipped - emitting completion events')

          emit('session-completed')

          const updateData = {
            pomodoroCount: timerStore.pomodoroCount,
            totalFocusTime: timerStore.totalFocusTime
          }
          console.log('🍅 [PomodoroTimer] Emitting session-updated for started pomodoro skip:', updateData)
          emit('session-updated', updateData)

        } else if (currentType === 'pomodoro' && !wasEverStarted) {
          // Timer was never started, just skip to break without counting
          console.log('🍅 [PomodoroTimer] Unstarted pomodoro was skipped - no completion events')

          // Still emit update but pomodoro count shouldn't have changed
          const updateData = {
            pomodoroCount: timerStore.pomodoroCount, // Should be same as oldCount
            totalFocusTime: timerStore.totalFocusTime
          }
          emit('session-updated', updateData)

        } else {
          console.log('🍅 [PomodoroTimer] Break was skipped - moving to next cycle')

          const updateData = {
            pomodoroCount: timerStore.pomodoroCount,
            totalFocusTime: timerStore.totalFocusTime
          }
          console.log('🍅 [PomodoroTimer] Emitting session-updated for break skip:', updateData)
          emit('session-updated', updateData)
        }

      } catch (error) {
        console.error('🍅 [PomodoroTimer] Skip failed:', error)
      }
    }

    const handleRestartTimer = async () => {
      console.log('🍅 [PomodoroTimer] handleRestartTimer called')
      await timerStore.restartTimer()
      console.log('🍅 [PomodoroTimer] Restart completed')
    }

    const handleEndSession = async () => {
      console.log('🍅 [PomodoroTimer] handleEndSession called')

      try {
        const endedSession = await timerStore.endSession()

        if (endedSession) {
          console.log('🍅 [PomodoroTimer] Session ended successfully:', endedSession.id)
          const sessionData = {
            pomodoroCount: endedSession.pomodoro_cycles_completed || 0,
            totalFocusTime: endedSession.duration || 0
          }
          console.log('🍅 [PomodoroTimer] Emitting end-session:', sessionData)
          emit('end-session', sessionData)
        } else {
          console.log('🍅 [PomodoroTimer] No session was ended')
        }
      } catch (error) {
        console.error('🍅 [PomodoroTimer] Failed to end session:', error)
      }
    }

    const handleUpdateSettings = async (newSettings: {
      pomodoroTime: number
      shortBreakTime: number
      longBreakTime: number
      longBreakInterval: number
      autoStartBreaks: boolean
      autoStartPomodoros: boolean
    }) => {
      console.log('🍅 [PomodoroTimer] handleUpdateSettings called:', newSettings)

      try {
        await timerStore.updateSettings(newSettings)
        showSettings.value = false
        console.log('🍅 [PomodoroTimer] Settings updated successfully')
      } catch (error) {
        console.error('🍅 [PomodoroTimer] Failed to update settings:', error)
      }
    }

    // FIX: Optimized session update emission
    const emitSessionUpdate = (reason: string = 'general') => {
      const now = Date.now()

      // Always emit for important events, throttle for routine updates
      const shouldEmit = reason === 'skip' || reason === 'completion' ||
        (now - lastSessionUpdateTime >= SESSION_UPDATE_THROTTLE)

      if (shouldEmit) {
        const updateData = {
          pomodoroCount: timerStore.pomodoroCount,
          totalFocusTime: timerStore.totalFocusTime
        }

        console.log(`🍅 [PomodoroTimer] Emitting session-updated (${reason}):`, updateData)
        emit('session-updated', updateData)
        lastSessionUpdateTime = now
      }
    }

    // Watch for timer completion
    watch(() => timerStore.timeLeft, (newTimeLeft, oldTimeLeft) => {
      if (oldTimeLeft !== undefined && oldTimeLeft === 1 && newTimeLeft === 0) {
        console.log('🍅 [PomodoroTimer] Timer completed detected via watcher')
        handleTimerComplete()
      }
    })

    // Watch for pomodoro count changes to emit session updates
    watch(() => timerStore.pomodoroCount, (newCount, oldCount) => {
      console.log('🍅 [PomodoroTimer] Pomodoro count changed:', oldCount, '->', newCount)

      if (newCount !== oldCount && oldCount !== undefined) {
        emitSessionUpdate('completion')
      }
    })

    // FIX: Throttled focus time updates to reduce spam
    watch(() => timerStore.totalFocusTime, (newTime, oldTime) => {
      if (newTime !== oldTime && oldTime !== undefined) {
        // Only emit session updates periodically for focus time changes
        emitSessionUpdate('focus-time')
      }
    })

    // Watch for auto-created sessions
    watch(() => timerStore.activeSession, (newSession, oldSession) => {
      console.log('🍅 [PomodoroTimer] Active session changed:', {
        old: oldSession?.id,
        new: newSession?.id
      })

      if (newSession && !oldSession) {
        console.log('🍅 [PomodoroTimer] New session detected, emitting session-auto-created')
        emit('session-auto-created', newSession)
      }
    })

    // FIX: Reduce logging frequency for state changes
    watch(() => timerStore.isRunning, (newRunning, oldRunning) => {
      if (oldRunning !== undefined) {
        console.log('🍅 [PomodoroTimer] Timer running state changed:', oldRunning, '->', newRunning)
      }
    })

    watch(() => timerStore.timerType, (newType, oldType) => {
      if (oldType !== undefined) {
        console.log('🍅 [PomodoroTimer] Timer type changed:', oldType, '->', newType)
      }
    })

    const handleTimerComplete = () => {
      const timerType = timerStore.timerType
      console.log('🍅 [PomodoroTimer] handleTimerComplete called for type:', timerType)
    }

    // Initialize on mount
    onMounted(async () => {
      console.log('🍅 [PomodoroTimer] Component mounted')

      // FIX: Don't call loadSettings on component mount since it's already initialized
      // The store is already initialized by App.vue, no need to reload settings
      console.log('🍅 [PomodoroTimer] Using existing store state, skipping settings reload')

      console.log('🍅 [PomodoroTimer] Final mounted state:', {
        isRunning: timerStore.isRunning,
        timerType: timerStore.timerType,
        timeLeft: timerStore.timeLeft,
        sessionActive: timerStore.sessionActive,
        activeSession: timerStore.activeSession?.id,
        pomodoroCount: timerStore.pomodoroCount,
        totalFocusTime: timerStore.totalFocusTime
      })
    })

    // Cleanup on unmount
    onBeforeUnmount(() => {
      console.log('🍅 [PomodoroTimer] Component unmounting')
      // The timer store handles its own cleanup
      // Individual components don't need to clean up the global timer
    })

    return {
      timerStore,
      showSettings,
      shouldDisablePlayButton,
      handleSwitchTimerType,
      handleToggleTimer,
      handleSkipTimer,
      handleRestartTimer,
      handleEndSession,
      handleUpdateSettings,
      // Icon imports for proper path resolution
      settingsIcon,
      restartIcon,
      playIcon,
      pauseIcon,
      skipIcon
    }
  }
})
</script>

<style scoped>
.timer-container {
  max-width: 550px;
  margin: 0 auto;
  width: 100%;
}

.timer-wrapper {
  border-radius: 10px;
  background-color: var(--color-timer-bg);
  border: 1px solid var(--color-timer-border);
  display: flex;
  width: 100%;
  padding: 19px 25px;
  flex-direction: column;
  align-items: stretch;
}

@media (max-width: 991px) {
  .timer-wrapper {
    max-width: 100%;
    padding: 19px 20px;
  }
}

.controls-section {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  color: var(--color-text-primary);
}

@media (max-width: 991px) {
  .controls-section {
    max-width: 100%;
  }
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
}

.session-title {
  font-size: 16px;
  font-weight: 700;
}

.settings-icon-container {
  display: flex;
  justify-content: flex-end;
}

.settings-icon {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 25px;
  cursor: pointer;
}

.button-group {
  display: flex;
  width: 100%;
  align-items: stretch;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
  justify-content: space-between;
  margin-top: 10px;
}

.timer-button {
  flex: 1;
  border-radius: 10px;
  min-height: 50px;
  padding: 12px 15px;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timer-button.active {
  background-color: var(--color-timer-button-active);
}

.timer-button.outlined {
  background-color: var(--color-timer-button-outlined);
  border: 1px solid var(--color-border-primary);
}

@media (max-width: 991px) {
  .button-group {
    flex-direction: column;
  }

  .timer-button {
    padding: 10px 15px;
  }
}

.time-display {
  color: var(--color-text-primary);
  font-size: 96px;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-weight: 700;
  text-align: center;
  align-self: center;
  margin-top: 21px;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  justify-items: center;
  gap: 0;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.time-minutes,
.time-seconds {
  min-width: 2ch;
  text-align: center;
  font-variant-numeric: tabular-nums;
}

.time-separator {
  font-weight: 700;
  line-height: 1;
  margin: 0 0.1em;
}

@media (max-width: 991px) {
  .time-display {
    font-size: 40px;
  }
}

.action-controls {
  align-self: center;
  display: grid;
  grid-template-columns: 1fr 70px 1fr;
  margin-top: 5px;
  width: fit-content;
  max-width: 100%;
  align-items: center;
  justify-items: center;
  gap: 15px;
  position: relative;
}

.play-button {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 70px;
  flex-shrink: 0;
  cursor: pointer;
  transition: opacity 0.2s ease, filter 0.2s ease;
  grid-column: 2;
}

.play-button.disabled {
  opacity: 0.4;
  cursor: not-allowed;
  filter: grayscale(50%);
}

.restart-button {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 40px;
  flex-shrink: 0;
  cursor: pointer;
  grid-column: 1;
  justify-self: end;
}

.skip-button {
  aspect-ratio: 1;
  object-fit: contain;
  object-position: center;
  width: 50px;
  margin: auto 0;
  flex-shrink: 0;
  cursor: pointer;
  grid-column: 3;
  justify-self: start;
}

.divider {
  background-color: var(--color-border-primary);
  height: 1px;
  margin-top: 24px;
}

@media (max-width: 991px) {
  .divider {
    max-width: 100%;
  }
}

.stats-section {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  grid-template-rows: 60px;
  grid-template-areas: "counter button focus";
  margin-top: 21px;
  align-items: start;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-size: 12px;
  color: var(--color-text-secondary);
  font-weight: 400;
  height: 60px;
}

@media (max-width: 991px) {
  .stats-section {
    max-width: 100%;
  }
}

.stat-text {
  text-align: center;
}

.pomodoro-counter-text {
  grid-area: counter;
  text-align: left;
  justify-self: start;
  align-self: center;
}

.focus-time-text {
  grid-area: focus;
  text-align: right;
  justify-self: end;
  align-self: center;
}

.end-session-container {
  grid-area: button;
  display: flex;
  justify-content: center;
  justify-self: center;
  align-self: center;
  align-items: center;
}

.end-session-button {
  padding: 10px 20px;
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 6px;
  font-family: Montserrat, -apple-system, Roboto, Helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.end-session-button:hover {
  background-color: var(--color-btn-primary-hover);
}
</style>
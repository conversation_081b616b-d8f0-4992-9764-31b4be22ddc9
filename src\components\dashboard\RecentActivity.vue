<template>
  <div class="recent-activity">
    <h2 class="section-title">Recent Activity</h2>

    <div class="activity-content">
      <div v-if="recentItems.length === 0" class="empty-state">
        <p>No recent activity found</p>
        <button class="create-button" @click="createNewNote">Get started with your first note</button>
      </div>
      <div v-else class="activity-list">
        <div
          v-for="item in recentItems.slice(0, 5)"
          :key="`${item.type}-${item.id}`"
          class="activity-item"
          @click="openItem(item)"
        >
          <div class="item-icon">
            <img
              :src="item.type === 'note' ? notesIcon : booksIcon"
              :alt="item.type === 'note' ? 'Note' : 'Book'"
              width="16"
              height="16"
            />
          </div>
          <div class="item-content">
            <div class="item-title">{{ item.title || (item.type === 'note' ? 'Untitled Note' : 'Untitled Book') }}</div>
            <div class="item-meta">{{ formatDate(item.date) }} • {{ item.type === 'note' ? 'Note' : 'Book' }}</div>
            <div v-if="item.preview" class="item-preview">{{ item.preview }}</div>
          </div>
          <div class="item-actions">
            <div class="item-info">
              <div class="item-location">{{ item.size }}</div>
            </div>
            <div class="action-buttons">
              <button
                class="action-btn"
                @click.stop="openItem(item)"
                title="Open"
              >
                <img src="/icons/edit-icon.svg" alt="Open" width="14" height="14" />
              </button>
            </div>
          </div>
        </div>
        <button v-if="recentItems.length > 5" class="view-all-button" @click="viewAllItems">
          View all notes
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useElectronAPI } from '../../useElectronAPI'
import type { Note, Book } from '../../types/electron-api'

// Import icons as modules for proper path resolution in production
import notesIcon from '/icons/notes-icon.svg'
import booksIcon from '/icons/books-icon.svg'

const router = useRouter()
const db = useElectronAPI()

interface RecentItem {
  id: number
  title: string
  date: string
  type: 'note' | 'book'
  preview?: string
  folder?: string
  size: string
}

const recentItems = ref<RecentItem[]>([])

const formatDate = (dateString?: string) => {
  if (!dateString) return 'Unknown date'

  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffTime / (1000 * 60))
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60))
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  // Less than 1 minute
  if (diffMinutes < 1) return 'Just now'

  // Less than 1 hour
  if (diffMinutes < 60) return `${diffMinutes} min ago`

  // Less than 24 hours
  if (diffHours < 24) return `${diffHours}h ago`

  // Less than 7 days
  if (diffDays < 7) return `${diffDays}d ago`

  // Less than 30 days
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}w ago`

  // Older than 30 days
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
  })
}

const getContentPreview = (content: string, maxLength: number = 80): string => {
  if (!content) return ''
  const cleanContent = content.replace(/[#*`\n\r]/g, ' ').replace(/\s+/g, ' ').trim()
  return cleanContent.length > maxLength ? cleanContent.substring(0, maxLength) + '...' : cleanContent
}

const buildFolderPath = (folderId: number | null | undefined, folderMap: Map<number, any>): string => {
  if (!folderId) return '/Root'

  const folder = folderMap.get(folderId)
  if (!folder) return '/Root'

  const path = []
  let currentFolder = folder

  // Build path by traversing up the parent chain
  while (currentFolder) {
    path.unshift(currentFolder.name)
    currentFolder = currentFolder.parent_id ? folderMap.get(currentFolder.parent_id) : null
  }

  return '/' + path.join('/')
}

const getLocationInfo = (folderPath: string, type: 'note' | 'book'): string => {
  if (type === 'book') {
    return '/Books'
  }

  return folderPath
}

const loadRecentData = async () => {
  try {
    // Load recent notes with folder information
    const notes = await db.notes.getAll()
    const folders = await db.folders.getAll()
    const folderMap = new Map(folders.map(f => [f.id, f]))

    const noteItems: RecentItem[] = notes.map(note => {
      const folderPath = buildFolderPath(note.folder_id, folderMap)
      return {
        id: note.id!,
        title: note.title || 'Untitled Note',
        date: note.updated_at || note.created_at || '',
        type: 'note' as const,
        preview: getContentPreview(note.content || ''),
        folder: folderPath,
        size: getLocationInfo(folderPath, 'note')
      }
    })

    // Load recent books
    const books = await db.books.getAll()
    const bookItems: RecentItem[] = books.map(book => ({
      id: book.id!,
      title: book.title || 'Untitled Book',
      date: book.updated_at || book.created_at || '',
      type: 'book' as const,
      preview: getContentPreview(book.description || ''),
      size: getLocationInfo('/Books', 'book')
    }))

    // Combine and sort by date
    const allItems = [...noteItems, ...bookItems]
    recentItems.value = allItems
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 10)
  } catch (error) {
    console.error('Failed to load recent activity:', error)
  }
}

// Navigation methods
const createNewNote = () => router.push('/notes')
const viewAllItems = () => {
  // Navigate to notes view as the primary content location
  // In the future, this could navigate to a dedicated "All Activity" view
  router.push('/notes')
}

const openItem = (item: RecentItem) => {
  if (item.type === 'note') {
    router.push(`/notes?noteId=${item.id}`)
  } else if (item.type === 'book') {
    router.push(`/books?bookId=${item.id}`)
  }
}

onMounted(() => {
  loadRecentData()
})

// Expose refresh method for parent components
defineExpose({
  refreshData: loadRecentData
})
</script>

<style scoped>
.recent-activity {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--color-text-primary);
  letter-spacing: -0.025em;
}

.activity-content {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: var(--color-dashboard-empty-text);
  background-color: var(--color-dashboard-empty-bg);
  border-radius: 8px;
  border: 1px dashed var(--color-border-secondary);
}

.empty-state p {
  margin-bottom: 20px;
  font-size: 1rem;
  font-weight: 500;
}

.create-button {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.create-button:hover {
  background-color: var(--color-btn-primary-hover);
  transform: translateY(-1px);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  min-height: 72px;
}

.activity-item:hover {
  background-color: var(--color-card-hover-bg);
  border-color: var(--color-border-hover);
  transform: translateX(2px);
}

.item-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background-color: var(--color-dashboard-action-icon-bg);
  border: 1px solid var(--color-dashboard-action-icon-border);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.activity-item:hover .item-icon {
  background-color: var(--color-bg-tertiary);
  border-color: var(--color-border-hover);
}

.item-icon img {
  width: 18px;
  height: 18px;
  filter: var(--icon-filter);
  transition: filter 0.2s ease;
}

.item-content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-meta {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-preview {
  font-size: 0.85rem;
  color: var(--color-text-tertiary);
  opacity: 0.7;
  line-height: 1.3;
  margin-top: 2px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.item-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  min-width: 80px;
}

.item-location {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.activity-item:hover .action-buttons {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  background-color: var(--color-bg-secondary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--color-border-secondary);
}

.action-btn:hover {
  background-color: var(--color-bg-tertiary);
  border-color: var(--color-border-hover);
  transform: scale(1.05);
}

.action-btn img {
  filter: var(--icon-filter);
  transition: filter 0.2s ease;
}

.view-all-button {
  margin-top: 16px;
  padding: 12px 20px;
  background: transparent;
  border: 1px solid var(--color-card-border);
  border-radius: 8px;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
}

.view-all-button:hover {
  background-color: var(--color-card-hover-bg);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .recent-activity {
    margin-bottom: 24px;
  }

  .section-title {
    font-size: 1.1rem;
    margin-bottom: 16px;
  }

  .activity-content {
    padding: 20px;
    border-radius: 10px;
  }

  .empty-state {
    padding: 32px 20px;
  }

  .empty-state p {
    font-size: 0.9rem;
    margin-bottom: 16px;
  }

  .create-button {
    padding: 10px 16px;
    font-size: 0.85rem;
  }

  .activity-item {
    padding: 12px;
    gap: 12px;
    min-height: 60px;
  }

  .item-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
  }

  .item-icon img {
    width: 16px;
    height: 16px;
  }

  .item-title {
    font-size: 0.9rem;
  }

  .item-meta {
    font-size: 0.75rem;
  }

  .item-preview {
    font-size: 0.8rem;
    -webkit-line-clamp: 1;
  }

  .item-info {
    min-width: 60px;
  }

  .item-location {
    font-size: 0.7rem;
  }

  .action-btn {
    width: 24px;
    height: 24px;
  }

  .action-btn img {
    width: 12px;
    height: 12px;
  }

  .view-all-button {
    margin-top: 12px;
    padding: 10px 16px;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .activity-content {
    padding: 16px;
  }

  .activity-item {
    padding: 10px;
    gap: 10px;
    min-height: 56px;
  }

  .item-icon {
    width: 32px;
    height: 32px;
  }

  .item-icon img {
    width: 14px;
    height: 14px;
  }

  .item-actions {
    gap: 8px;
  }

  .item-info {
    min-width: 50px;
  }

  .action-buttons {
    opacity: 1; /* Always show on mobile */
  }

  .action-btn {
    width: 20px;
    height: 20px;
  }

  .action-btn img {
    width: 10px;
    height: 10px;
  }
}
</style>

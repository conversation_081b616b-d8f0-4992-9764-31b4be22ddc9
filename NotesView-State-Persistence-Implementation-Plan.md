# NotesView State Persistence Implementation Plan

## 1. Architecture Overview

### Current Problem Analysis
- **Component Lifecycle**: NotesView is completely unmounted/remounted on navigation
- **State Loss**: All reactive state (selectedNote, scroll positions, cursor position) is lost
- **Default Behavior**: Always selects first note on mount (lines 407-410 in NotesView.vue)
- **No Persistence**: No mechanism to save/restore view state

### Solution Architecture
Create a **NotesViewStore** using Pinia that mirrors the timerStore pattern, providing:
- Persistent state management across navigation
- Editor position tracking
- Scroll position preservation
- Seamless state restoration

## 2. State Structure Design

### Core State Interface
```typescript
export interface NotesViewState {
  // Note selection state
  selectedNoteId: number | null
  selectedFolderId: string | number
  
  // Multi-selection state
  selectedNoteIds: number[]
  lastSelectedNoteId: number | null
  
  // Search and filtering state
  searchQuery: string
  
  // Editor state
  editorState: {
    cursorPosition: number | null
    scrollTop: number
    hasUnsavedChanges: boolean
  } | null
  
  // UI state
  notesListScrollTop: number
  
  // Loading state
  isInitialized: boolean
  lastViewTime: number
}
```

### Persistence Strategy
- **Memory-based**: Store state in Pinia store (survives navigation)
- **Session-based**: No localStorage persistence (resets on app restart)
- **Selective restoration**: Only restore if returning within reasonable time window

## 3. Implementation Plan

### Phase 1: Create NotesViewStore

**File**: `src/stores/notesViewStore.ts`

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface NotesViewState {
  selectedNoteId: number | null
  selectedFolderId: string | number
  selectedNoteIds: number[]
  lastSelectedNoteId: number | null
  searchQuery: string
  editorState: {
    cursorPosition: number | null
    scrollTop: number
    hasUnsavedChanges: boolean
  } | null
  notesListScrollTop: number
  isInitialized: boolean
  lastViewTime: number
}

export const useNotesViewStore = defineStore('notesView', () => {
  // ===== STATE =====
  const state = ref<NotesViewState>({
    selectedNoteId: null,
    selectedFolderId: 'all',
    selectedNoteIds: [],
    lastSelectedNoteId: null,
    searchQuery: '',
    editorState: null,
    notesListScrollTop: 0,
    isInitialized: false,
    lastViewTime: 0
  })

  // ===== COMPUTED =====
  const shouldRestoreState = computed(() => {
    const timeSinceLastView = Date.now() - state.value.lastViewTime
    const maxRestoreTime = 5 * 60 * 1000 // 5 minutes
    return state.value.isInitialized && timeSinceLastView < maxRestoreTime
  })

  // ===== ACTIONS =====
  
  // Save current note selection
  const saveNoteSelection = (noteId: number | null, folderId: string | number = 'all') => {
    state.value.selectedNoteId = noteId
    state.value.selectedFolderId = folderId
    state.value.lastViewTime = Date.now()
  }

  // Save multi-selection state
  const saveMultiSelection = (noteIds: number[], lastSelectedId: number | null) => {
    state.value.selectedNoteIds = [...noteIds]
    state.value.lastSelectedNoteId = lastSelectedId
  }

  // Save search state
  const saveSearchState = (query: string) => {
    state.value.searchQuery = query
  }

  // Save editor state
  const saveEditorState = (cursorPosition: number | null, scrollTop: number, hasUnsavedChanges: boolean = false) => {
    state.value.editorState = {
      cursorPosition,
      scrollTop,
      hasUnsavedChanges
    }
  }

  // Save notes list scroll position
  const saveNotesListScroll = (scrollTop: number) => {
    state.value.notesListScrollTop = scrollTop
  }

  // Mark as initialized
  const markInitialized = () => {
    state.value.isInitialized = true
    state.value.lastViewTime = Date.now()
  }

  // Clear all state (for app restart or explicit reset)
  const clearState = () => {
    state.value = {
      selectedNoteId: null,
      selectedFolderId: 'all',
      selectedNoteIds: [],
      lastSelectedNoteId: null,
      searchQuery: '',
      editorState: null,
      notesListScrollTop: 0,
      isInitialized: false,
      lastViewTime: 0
    }
  }

  // Get restoration data
  const getRestorationData = () => {
    if (!shouldRestoreState.value) {
      return null
    }
    
    return {
      selectedNoteId: state.value.selectedNoteId,
      selectedFolderId: state.value.selectedFolderId,
      selectedNoteIds: [...state.value.selectedNoteIds],
      lastSelectedNoteId: state.value.lastSelectedNoteId,
      searchQuery: state.value.searchQuery,
      editorState: state.value.editorState ? { ...state.value.editorState } : null,
      notesListScrollTop: state.value.notesListScrollTop
    }
  }

  return {
    // State (readonly)
    state: computed(() => state.value),
    shouldRestoreState,
    
    // Actions
    saveNoteSelection,
    saveMultiSelection,
    saveSearchState,
    saveEditorState,
    saveNotesListScroll,
    markInitialized,
    clearState,
    getRestorationData
  }
})
```

### Phase 2: Modify NotesView.vue Integration

**Key Changes to NotesView.vue**:

1. **Import and Initialize Store**:
```typescript
import { useNotesViewStore } from '../stores/notesViewStore'

setup() {
  const notesViewStore = useNotesViewStore()
  // ... existing code
}
```

2. **Modify onMounted Logic**:
```typescript
onMounted(() => {
  loadNotes().then(() => {
    // Check for restoration data first
    const restorationData = notesViewStore.getRestorationData()

    if (restorationData) {
      console.log('🔄 Restoring NotesView state from store')

      // Restore search state
      if (restorationData.searchQuery) {
        searchQuery.value = restorationData.searchQuery
      }

      // Restore folder selection
      selectedFolder.value = restorationData.selectedFolderId

      // Restore note selection
      if (restorationData.selectedNoteId) {
        const noteToRestore = notes.value.find(note => note.id === restorationData.selectedNoteId)
        if (noteToRestore) {
          selectNote(noteToRestore)

          // Restore multi-selection if applicable
          if (restorationData.selectedNoteIds.length > 0) {
            selectedNotes.value = notes.value.filter(note =>
              restorationData.selectedNoteIds.includes(note.id!)
            )
            lastSelectedNoteId.value = restorationData.lastSelectedNoteId
          }

          // Schedule editor and scroll restoration
          nextTick(() => {
            restoreScrollPositions(restorationData)
          })

          notesViewStore.markInitialized()
          return
        }
      }
    }

    // Fallback to existing logic (URL params or first note)
    const noteIdFromQuery = route.query.noteId
    if (noteIdFromQuery && typeof noteIdFromQuery === 'string') {
      const noteId = parseInt(noteIdFromQuery, 10)
      const noteToSelect = notes.value.find(note => note.id === noteId)
      if (noteToSelect) {
        selectNote(noteToSelect)
        notesViewStore.markInitialized()
        return
      }
    }

    // Select first note as last resort
    if (notes.value.length > 0) {
      selectNote(notes.value[0])
    }

    notesViewStore.markInitialized()
  })

  // ... rest of existing onMounted logic
})
```

3. **Add State Saving Hooks**:
```typescript
// Watch selectedNote changes
watch(() => selectedNote.value, (newNote) => {
  if (newNote?.id) {
    notesViewStore.saveNoteSelection(newNote.id, selectedFolder.value)
  }
})

// Watch search query changes
watch(() => searchQuery.value, (newQuery) => {
  notesViewStore.saveSearchState(newQuery)
})

// Watch multi-selection changes
watch(() => selectedNotes.value, (newSelection) => {
  const noteIds = newSelection.map(note => note.id!).filter(id => id !== undefined)
  notesViewStore.saveMultiSelection(noteIds, lastSelectedNoteId.value)
})

// Save notes list scroll position
const saveNotesListScrollPosition = () => {
  const notesList = document.querySelector('.notes-list')
  if (notesList) {
    notesViewStore.saveNotesListScroll(notesList.scrollTop)
  }
}

// Add scroll listener
onMounted(() => {
  const notesList = document.querySelector('.notes-list')
  if (notesList) {
    notesList.addEventListener('scroll', saveNotesListScrollPosition)
  }
})

onBeforeUnmount(() => {
  const notesList = document.querySelector('.notes-list')
  if (notesList) {
    notesList.removeEventListener('scroll', saveNotesListScrollPosition)
  }
})
```

### Phase 3: NoteEditor.vue Integration

**Key Changes to NoteEditor.vue**:

1. **Add Store Integration**:
```typescript
import { useNotesViewStore } from '../../stores/notesViewStore'

setup(props, { emit }) {
  const notesViewStore = useNotesViewStore()

  // ... existing code
}
```

2. **Add Editor State Tracking**:
```typescript
// Track cursor position and scroll
const saveEditorState = () => {
  if (!editor.value) return

  const selection = editor.value.state.selection
  const cursorPosition = selection.$anchor.pos

  const editorElement = document.querySelector('.tiptap-editor .ProseMirror')
  const scrollTop = editorElement?.scrollTop || 0

  notesViewStore.saveEditorState(cursorPosition, scrollTop)
}

// Debounced state saving
let saveStateTimeout: NodeJS.Timeout | null = null
const debouncedSaveState = () => {
  if (saveStateTimeout) clearTimeout(saveStateTimeout)
  saveStateTimeout = setTimeout(saveEditorState, 500)
}

// Add event listeners for state tracking
watch(editor, (newEditor) => {
  if (newEditor) {
    // ... existing setup code

    // Add state tracking
    newEditor.on('selectionUpdate', debouncedSaveState)
    newEditor.on('update', debouncedSaveState)

    // Add scroll listener
    const editorElement = document.querySelector('.tiptap-editor .ProseMirror')
    if (editorElement) {
      editorElement.addEventListener('scroll', debouncedSaveState)
    }
  }
})
```

3. **Add State Restoration**:
```typescript
// Restore editor state when note changes
watch(() => props.note.id, async (newNoteId, oldNoteId) => {
  if (newNoteId !== oldNoteId && editor.value) {
    editor.value.commands.setContent(props.note.html_content || '<p></p>')

    // Check if we should restore editor state
    const restorationData = notesViewStore.getRestorationData()
    if (restorationData?.editorState && restorationData.selectedNoteId === newNoteId) {
      await nextTick()

      // Restore cursor position
      if (restorationData.editorState.cursorPosition !== null) {
        editor.value.commands.setTextSelection(restorationData.editorState.cursorPosition)
      }

      // Restore scroll position
      if (restorationData.editorState.scrollTop > 0) {
        const editorElement = document.querySelector('.tiptap-editor .ProseMirror')
        if (editorElement) {
          editorElement.scrollTop = restorationData.editorState.scrollTop
        }
      }
    }

    // ... existing font application logic
  }
})
```

### Phase 4: Scroll Position Restoration Helper

**Add to NotesView.vue**:
```typescript
const restoreScrollPositions = async (restorationData: any) => {
  await nextTick()

  // Restore notes list scroll position
  if (restorationData.notesListScrollTop > 0) {
    const notesList = document.querySelector('.notes-list')
    if (notesList) {
      notesList.scrollTop = restorationData.notesListScrollTop
    }
  }

  // Editor scroll restoration is handled in NoteEditor.vue
}
```

## 4. Integration Points

### Store Registration
**In `src/main.ts`** (no changes needed - Pinia auto-registers stores)

### Type Definitions
**Add to `src/types/index.ts`**:
```typescript
export interface NotesViewState {
  selectedNoteId: number | null
  selectedFolderId: string | number
  selectedNoteIds: number[]
  lastSelectedNoteId: number | null
  searchQuery: string
  editorState: {
    cursorPosition: number | null
    scrollTop: number
    hasUnsavedChanges: boolean
  } | null
  notesListScrollTop: number
  isInitialized: boolean
  lastViewTime: number
}
```

## 5. Safety Measures

### Backward Compatibility
- All existing functionality remains unchanged
- Store is optional - if it fails, falls back to current behavior
- No breaking changes to existing API

### Error Handling
```typescript
// Wrap restoration in try-catch
try {
  const restorationData = notesViewStore.getRestorationData()
  // ... restoration logic
} catch (error) {
  console.warn('Failed to restore NotesView state:', error)
  // Fall back to default behavior
}
```

### Performance Considerations
- Debounced state saving (500ms)
- Time-based restoration window (5 minutes)
- Minimal memory footprint
- No database persistence

## 6. Testing Strategy

### Manual Testing Scenarios
1. **Basic Navigation**: Notes → Timer → Notes (should restore last note)
2. **Search Persistence**: Search for text → navigate away → return (should restore search)
3. **Scroll Position**: Scroll in notes list and editor → navigate → return (should restore positions)
4. **Multi-selection**: Select multiple notes → navigate → return (should restore selection)
5. **Time Expiry**: Wait 6+ minutes → navigate → return (should not restore, use default)

### Edge Cases
- Empty notes list
- Deleted notes (selected note no longer exists)
- Changed folder structure
- Editor initialization failures

## 7. Implementation Order

1. **Create NotesViewStore** (Phase 1)
2. **Basic note selection restoration** (Phase 2 - partial)
3. **Search and folder state restoration** (Phase 2 - continued)
4. **Editor cursor/scroll restoration** (Phase 3)
5. **Notes list scroll restoration** (Phase 4)
6. **Multi-selection restoration** (Phase 2 - final)
7. **Testing and refinement**

## 8. Key Benefits

### User Experience
- **Seamless Navigation**: Return to exact same position when switching views
- **Context Preservation**: Maintain reading flow and work context
- **Reduced Friction**: No need to re-navigate to desired note/position

### Technical Benefits
- **Memory Efficient**: No persistent storage, minimal memory usage
- **Performance Optimized**: Debounced updates, time-based expiry
- **Maintainable**: Follows established Pinia patterns in codebase
- **Safe**: Graceful fallbacks, no breaking changes

### Implementation Safety
- **Non-Breaking**: All existing functionality preserved
- **Optional**: Store failures fall back to current behavior
- **Time-Limited**: State expires after 5 minutes to prevent stale data
- **Error-Resilient**: Comprehensive error handling and fallbacks

This plan provides a comprehensive, safe, and maintainable solution that follows the established patterns in the codebase while preserving all existing functionality and significantly improving the user experience when navigating between views.

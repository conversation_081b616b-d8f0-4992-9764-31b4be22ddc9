# SQLite3 Windows Build Fix

## Files Modified
- `electron-builder.json5`
- `package.json`

## What Was The Issue
The build was failing on Windows with two main problems:
1. electron-builder was trying to rebuild native modules from source but couldn't find `binding.gyp`
2. SQLite3 native bindings weren't being properly compiled for the Electron version

Error message:
```
gyp: binding.gyp not found (cwd: C:\Users\<USER>\Desktop\Noti\Noti) while trying to load binding.gyp
```

## How It Was Fixed

### 1. Disabled Automatic Native Module Rebuild in electron-builder
Changed in `electron-builder.json5`:
- `npmRebuild: false` (was true)
- `buildDependenciesFromSource: false` (was true)
- `nodeGypRebuild: false` (was true)

This prevents electron-builder from trying to rebuild modules during packaging.

### 2. Added Proper Postinstall Script
Updated `package.json` to use electron-rebuild specifically for sqlite3:
- `"postinstall": "electron-rebuild -f -w sqlite3"`
- Added `"electron-rebuild": "electron-rebuild -f -w sqlite3"` for manual rebuilding

### 3. Understanding the Build Process
- **Vite Build**: Compiles the Vue.js frontend (HTML/CSS/JS)
- **Electron-Builder**: Packages everything into a Windows executable

## Steps to Build Successfully

1. **Clean Install Dependencies**
   ```powershell
   # Remove node_modules and package-lock
   Remove-Item -Recurse -Force node_modules
   Remove-Item package-lock.json
   
   # Fresh install
   npm install
   ```

2. **If Postinstall Fails, Manually Rebuild**
   ```powershell
   npm run electron-rebuild
   ```

3. **Build for Windows**
   ```powershell
   npm run build:win
   ```

## Alternative Solutions If Still Having Issues

### Option 1: Use Prebuilt Binaries
```powershell
# Install sqlite3 with prebuilt binaries
npm install sqlite3@5.1.7 --save-exact
```

### Option 2: Install Windows Build Tools
```powershell
# Run as Administrator
npm install --global windows-build-tools
```

### Option 3: Switch to better-sqlite3
If issues persist, consider migrating to `better-sqlite3` which has better Electron support:
```powershell
npm uninstall sqlite3
npm install better-sqlite3
```

Then update imports from `sqlite3` to `better-sqlite3` in the codebase.

## Why This Works
- electron-rebuild properly compiles sqlite3 for your specific Electron version
- Disabling npmRebuild prevents conflicting rebuild attempts
- The asarUnpack configuration ensures sqlite3 binaries are accessible at runtime
<template>
  <div class="modal-content">
      <h3>Move Items</h3>
      <div class="move-info">
        <p>Select destination folder for the selected item(s):</p>
        <div class="selected-items-summary">
          <template v-if="folderCount > 0 && noteCount > 0">
            <p>{{ folderCount }} folder(s) and {{ noteCount }} note(s)</p>
          </template>
          <template v-else-if="folderCount > 0">
            <p>{{ folderCount }} folder(s)</p>
          </template>
          <template v-else>
            <p>{{ noteCount }} note(s)</p>
          </template>
        </div>
      </div>
      
      <div class="folder-list-container">
        <div class="folder-list">
          <!-- Root folder option -->
          <div 
            class="folder-item"
            :class="{ 
              'selected': selectedFolderId === null,
              'disabled': isRootDisabled 
            }"
            @click="!isRootDisabled && selectFolder(null)"
          >
            <FolderIcon :color="undefined" :size="18" :isOpen="false" class="folder-icon" />
            <span :class="{ 'disabled-text': isRootDisabled }">All Folders (Root)</span>
            <span v-if="isRootDisabled" class="disabled-reason">(Current folder)</span>
          </div>
          
          <!-- Available folders -->
          <div v-for="folder in availableFolders" :key="folder.id" 
               class="folder-item"
               :class="{ 
                 'selected': selectedFolderId === folder.id,
                 'disabled': isDisabled(folder)
               }"
               @click="!isDisabled(folder) && selectFolder(folder.id)"
          >
            <div class="folder-indent" :style="{ paddingLeft: `${folder.level * 16}px` }">
              <FolderIcon :color="folder.color || undefined" :size="18" :isOpen="false" class="folder-icon" />
              <span :class="{ 'disabled-text': isDisabled(folder) }">{{ folder.name }}</span>
              <span v-if="isDisabled(folder)" class="disabled-reason">(Can't move here)</span>
            </div>
          </div>
          
          <div v-if="availableFolders.length === 0" class="no-folders">
            <p>No available destination folders</p>
          </div>
        </div>
      </div>

      <div class="selected-destination" v-if="selectedFolderId !== undefined && !errorMessage">
        <div class="destination-info">
          <FolderIcon :color="getSelectedFolderColor" :size="18" :isOpen="false" class="folder-icon" />
          <span>Move to: <strong>{{ getSelectedFolderName }}</strong></span>
        </div>
      </div>
      
      <div class="modal-buttons">
        <button @click="cancel" class="btn btn-secondary" :disabled="isLoading">Cancel</button>
        <div class="button-container">
          <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
          <button 
            @click="confirm" 
            class="btn btn-primary"
            :disabled="!canConfirm || isLoading"
          >
            <span v-if="isLoading" class="loading-spinner"></span>
            <span>{{ isLoading ? 'Moving...' : 'Move' }}</span>
          </button>
        </div>
      </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import FolderIcon from '../icons/FolderIcon.vue';

interface Folder {
  id: number;
  name: string;
  parent_id: number | null;
  color?: string | null;
  level?: number;
}

interface FolderWithLevel extends Folder {
  level: number;
}

interface Item {
  id: number;
  type: 'folder' | 'note';
}

export default defineComponent({
  name: 'MoveFolderModal',
  components: {
    FolderIcon
  },
  props: {
    folders: {
      type: Array as PropType<Folder[]>,
      required: true
    },
    items: {
      type: Array as PropType<Item[]>,
      required: true
    },
    currentFolderId: {
      type: Number as PropType<number | null>,
      default: null
    }
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const selectedFolderId = ref<number | null>(null);
    const isLoading = ref(false);
    
    // Check if root folder should be disabled
    const isRootDisabled = computed(() => {
      return props.currentFolderId === null;
    });
    
    // Count folders and notes
    const folderCount = computed(() => 
      props.items.filter(item => item.type === 'folder').length
    );
    
    const noteCount = computed(() => 
      props.items.filter(item => item.type === 'note').length
    );
    
    // Process folders to add level information for indentation
    const availableFolders = computed(() => {
      const itemFolderIds = props.items
        .filter(item => item.type === 'folder')
        .map(item => item.id);
      
      // Build a tree structure to determine folder levels
      const result: FolderWithLevel[] = [];
      
      // Helper function to build folder hierarchy with levels
      const buildFolderHierarchy = (parentId: number | null, level: number) => {
        const children = props.folders.filter(f => f.parent_id === parentId);
        
        children.forEach(folder => {
          // Skip folders that are being moved or are descendants of folders being moved
          if (!itemFolderIds.includes(folder.id)) {
            result.push({
              ...folder,
              level
            });
            // Recursively process children
            buildFolderHierarchy(folder.id, level + 1);
          }
        });
      };
      
      // Start building from the root
      buildFolderHierarchy(null, 0);
      
      return result;
    });
    
    // Check if a folder should be disabled (can't move to itself or its descendants)
    const isDisabled = (folder: Folder) => {
      const itemFolderIds = props.items
        .filter(item => item.type === 'folder')
        .map(item => item.id);
      
      // Can't move to itself
      if (itemFolderIds.includes(folder.id)) {
        return true;
      }
      
      // Build folder ancestry map for quick lookup
      const folderMap = new Map(props.folders.map(f => [f.id, f]));
      
      // Function to check if a folder is a descendant of any selected folder
      const isDescendantOfAnySelectedFolder = (folderToCheck: Folder): boolean => {
        // Get all ancestors of this folder
        const ancestors: number[] = [];
        let current: Folder | undefined = folderToCheck;
        
        while (current && current.parent_id !== null) {
          ancestors.push(current.parent_id);
          current = folderMap.get(current.parent_id);
        }
        
        // Check if any selected folder is in the ancestors list
        return ancestors.some(ancestorId => itemFolderIds.includes(ancestorId));
      };
      
      // Check if target folder is a descendant of any selected folder
      return isDescendantOfAnySelectedFolder(folder);
    };
    
    // Check if Move button should be enabled
    const canConfirm = computed(() => {
      // If no destination is selected yet
      if (selectedFolderId.value === undefined) {
        return false;
      }
      
      // If current folder is selected destination, disable Move button
      if (selectedFolderId.value === props.currentFolderId) {
        return false;
      }
      
      // Check if any move would create a circular reference
      const itemFolderIds = props.items
        .filter(item => item.type === 'folder')
        .map(item => item.id);
      
      if (selectedFolderId.value !== null && itemFolderIds.includes(selectedFolderId.value)) {
        return false;
      }
      
      return true;
    });
    
    // Error message for invalid selections
    const errorMessage = computed(() => {
      // If no destination is selected yet
      if (selectedFolderId.value === undefined) {
        return "Select a destination folder";
      }
      
      // If current folder is selected destination
      if (selectedFolderId.value === props.currentFolderId) {
        return "Items are already in this folder";
      }
      
      // Check if any move would create a circular reference
      const itemFolderIds = props.items
        .filter(item => item.type === 'folder')
        .map(item => item.id);
      
      if (selectedFolderId.value !== null && itemFolderIds.includes(selectedFolderId.value)) {
        return "Cannot move a folder to itself";
      }
      
      return "";
    });
    
    // Get name of the selected folder for display
    const getSelectedFolderName = computed(() => {
      if (selectedFolderId.value === null) {
        return "All Folders (Root)";
      }

      const folder = props.folders.find(f => f.id === selectedFolderId.value);
      return folder ? folder.name : "";
    });

    // Get color of the selected folder for display
    const getSelectedFolderColor = computed(() => {
      if (selectedFolderId.value === null) {
        return null;
      }

      const folder = props.folders.find(f => f.id === selectedFolderId.value);
      return folder ? folder.color || null : null;
    });
    
    const selectFolder = (folderId: number | null) => {
      selectedFolderId.value = folderId;
    };
    
    const confirm = () => {
      isLoading.value = true;
      emit('confirm', selectedFolderId.value);
      
      // Note: The loading state should be reset by the parent component
      // after the move operation is complete, but we'll add a safeguard
      // to reset it after a few seconds in case the parent doesn't handle it
      setTimeout(() => {
        isLoading.value = false;
      }, 5000);
    };
    
    const cancel = () => {
      emit('cancel');
    };
    
    return {
      selectedFolderId,
      isLoading,
      folderCount,
      noteCount,
      availableFolders,
      isDisabled,
      isRootDisabled,
      canConfirm,
      errorMessage,
      getSelectedFolderName,
      getSelectedFolderColor,
      selectFolder,
      confirm,
      cancel
    };
  }
});
</script>

<style scoped>
.modal-content {
  background-color: var(--color-modal-bg);
  padding: 32px; /* Increased padding */
  border-radius: 12px; /* Softer corners */
  box-shadow: 0 8px 24px var(--color-card-hover-shadow); /* Enhanced shadow */
  width: 450px; /* Fixed width */
  max-width: 90%;
  font-family: 'Montserrat', sans-serif; /* Consistent font */
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 16px; /* Adjusted spacing */
  font-size: 24px; /* Larger title */
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center; /* Centered title */
}

.move-info {
  margin-bottom: 20px;
}

.move-info p {
  margin: 0 0 10px 0;
  color: var(--color-text-secondary);
  font-size: 14px;
}

.selected-items-summary {
  background-color: var(--color-bg-secondary);
  padding: 10px;
  border-radius: 6px;
  margin-top: 8px;
}

.selected-items-summary p {
  margin: 0;
  font-weight: 500;
  color: var(--color-text-primary);
}

.folder-list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  margin-bottom: 16px;
}

/* Custom scrollbar styling for folder list container */
.folder-list-container::-webkit-scrollbar {
  width: 8px;
}

.folder-list-container::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 4px;
}

.folder-list-container::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.folder-list-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.folder-list {
  padding: 8px 0;
}

.folder-item {
  padding: 10px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.folder-item:hover:not(.disabled) {
  background-color: var(--color-nav-item-hover);
}

.folder-item.selected {
  background-color: var(--color-nav-item-active);
}

.folder-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-bg-tertiary);
  position: relative;
}

.folder-item.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
  pointer-events: none;
}

.folder-indent {
  display: flex;
  align-items: center;
  width: 100%;
}

.folder-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.disabled-text {
  color: var(--color-text-muted);
}

.disabled-reason {
  font-size: 11px;
  color: var(--color-error);
  margin-left: 8px;
  font-style: italic;
}

.no-folders {
  padding: 15px;
  text-align: center;
  color: var(--color-text-secondary);
}

.selected-destination {
  background-color: var(--color-nav-item-active);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 16px;
}

.destination-info {
  display: flex;
  align-items: center;
}

.modal-buttons {
  display: flex;
  justify-content: space-between; /* Space between cancel and confirm buttons */
  align-items: center;
  gap: 12px; /* Space between buttons */
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-bottom: 4px;
  text-align: right;
}

.loading-spinner {
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-btn-primary-text);
  animation: spin 1s ease-in-out infinite;
  margin-right: 6px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.btn {
  padding: 10px 18px;
  border: none;
  border-radius: 8px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-btn-primary-hover);
}

.btn-primary:disabled {
  background-color: var(--color-bg-tertiary);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-btn-secondary-hover);
}

.btn-secondary:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
</style>
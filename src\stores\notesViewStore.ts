import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface NotesViewState {
  selectedNoteId: number | null
  selectedFolderId: string | number
  selectedNoteIds: number[]
  lastSelectedNoteId: number | null
  searchQuery: string
  editorState: {
    cursorPosition: number | null
    scrollTop: number
    hasUnsavedChanges: boolean
  } | null
  notesListScrollTop: number
  isInitialized: boolean
  lastViewTime: number
}

export const useNotesViewStore = defineStore('notesView', () => {
  // ===== STATE =====
  const state = ref<NotesViewState>({
    selectedNoteId: null,
    selectedFolderId: 'all',
    selectedNoteIds: [],
    lastSelectedNoteId: null,
    searchQuery: '',
    editorState: null,
    notesListScrollTop: 0,
    isInitialized: false,
    lastViewTime: 0
  })

  // ===== COMPUTED =====
  const shouldRestoreState = computed(() => {
    const timeSinceLastView = Date.now() - state.value.lastViewTime
    const maxRestoreTime = 5 * 60 * 1000 // 5 minutes
    return state.value.isInitialized && timeSinceLastView < maxRestoreTime
  })

  // ===== ACTIONS =====
  
  // Save current note selection
  const saveNoteSelection = (noteId: number | null, folderId: string | number = 'all') => {
    state.value.selectedNoteId = noteId
    state.value.selectedFolderId = folderId
    state.value.lastViewTime = Date.now()
  }

  // Save multi-selection state
  const saveMultiSelection = (noteIds: number[], lastSelectedId: number | null) => {
    state.value.selectedNoteIds = [...noteIds]
    state.value.lastSelectedNoteId = lastSelectedId
  }

  // Save search state
  const saveSearchState = (query: string) => {
    state.value.searchQuery = query
  }

  // Save editor state
  const saveEditorState = (cursorPosition: number | null, scrollTop: number, hasUnsavedChanges: boolean = false) => {
    state.value.editorState = {
      cursorPosition,
      scrollTop,
      hasUnsavedChanges
    }
  }

  // Save notes list scroll position
  const saveNotesListScroll = (scrollTop: number) => {
    state.value.notesListScrollTop = scrollTop
  }

  // Mark as initialized
  const markInitialized = () => {
    state.value.isInitialized = true
    state.value.lastViewTime = Date.now()
  }

  // Clear all state (for app restart or explicit reset)
  const clearState = () => {
    state.value = {
      selectedNoteId: null,
      selectedFolderId: 'all',
      selectedNoteIds: [],
      lastSelectedNoteId: null,
      searchQuery: '',
      editorState: null,
      notesListScrollTop: 0,
      isInitialized: false,
      lastViewTime: 0
    }
  }

  // Get restoration data
  const getRestorationData = () => {
    if (!shouldRestoreState.value) {
      return null
    }
    
    return {
      selectedNoteId: state.value.selectedNoteId,
      selectedFolderId: state.value.selectedFolderId,
      selectedNoteIds: [...state.value.selectedNoteIds],
      lastSelectedNoteId: state.value.lastSelectedNoteId,
      searchQuery: state.value.searchQuery,
      editorState: state.value.editorState ? { ...state.value.editorState } : null,
      notesListScrollTop: state.value.notesListScrollTop
    }
  }

  return {
    // State (readonly)
    state: computed(() => state.value),
    shouldRestoreState,
    
    // Actions
    saveNoteSelection,
    saveMultiSelection,
    saveSearchState,
    saveEditorState,
    saveNotesListScroll,
    markInitialized,
    clearState,
    getRestorationData
  }
})
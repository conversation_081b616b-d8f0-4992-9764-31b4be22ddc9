<template>
  <div class="notes-view">
    <div class="notes-container">
      <!-- Notes Sidebar -->      <aside class="notes-sidebar">        <div class="sidebar-header">          <div class="search-bar" @click="focusSearch">
            <img src="/icons/search-icon.svg" class="search-icon" alt="Search" />
            <input
              ref="searchInputRef"
              type="text"
              placeholder="Search notes..."
              v-model="searchQuery"
              @input="filterNotes"
            />
            <button class="clear-search" v-if="searchQuery" @click.stop="clearSearch">
              <img src="/icons/close-icon.svg" class="close-icon" alt="Clear" />
            </button>
          </div>

          <button class="new-note-button" @click="createNewNote">
            <img src="/icons/plus-icon.svg" class="plus-icon" alt="+" />
            <span class="button-text">New Note</span>
          </button>

          <div class="filter-section">
            <!-- Custom dropdown implementation -->
            <div class="dropdown" ref="dropdownContainer">
              <div class="folder-select" @click="toggleDropdown">
                <span>{{ getSelectedFolderName() }}</span>
                <img src="/icons/dropdown-arrow-icon.svg" class="dropdown-arrow" :class="{ 'open': dropdownOpen }" alt="▼" />
              </div>

              <!-- Custom dropdown options -->
              <div v-if="dropdownOpen" class="dropdown-options" ref="dropdownOptions" @wheel="handleScroll">
                <div class="option" @click="selectFolderOption('all')">
                  All Notes
                </div>
                <div v-for="folder in folders"
                     :key="folder.id"
                     class="option"
                     @click="selectFolderOption(folder.id)">
                  {{ folder.name }}
                </div>
              </div>
            </div>            <button class="trash-button"
                    @click="handleTrashButtonClick()"
                    :disabled="!selectedNote && selectedNotes.length === 0"
                    :class="{ 'disabled': !selectedNote && selectedNotes.length === 0 }">
              <img src="/icons/trash-icon.svg" class="trash-icon" alt="🗑️" />
            </button>
          </div>
        </div>        <div class="notes-list"
             @click.self="handleNotesListClick"
             :class="{ 'selection-mode': selectedNotes.length > 0 }">
          <div v-if="loading" class="loading-state">
            <p>Loading notes...</p>          </div><div v-else-if="filteredNotes.length === 0" class="empty-state">
            <p>No notes found</p>
          </div><NoteCard v-else v-for="note in filteredNotes" :key="note.id" :title="note.title"
            :content="note.content || ''" :date="formatDate(note.updated_at || note.created_at)"            :path="note.folder_id ? getFolderPath(note.folder_id) : 'Uncategorized'"
            :isActive="!!(selectedNote && note.id && selectedNote.id === note.id)"
            :isSelected="selectedNotes.some(n => n.id === note.id)"
            @select="selectNote(note)"
            @multiSelect="handleMultiSelect(note, $event)"
            @rightClick="handleRightClick(note, $event)" /></div>
      </aside>

      <!-- Note Editor Section -->
      <div class="editor-wrapper">
        <!-- Added header with title and action buttons -->
        <header v-if="selectedNote" class="editor-header">
          <div class="header-content">
            <input type="text" class="note-title-input" placeholder="Note Title" :value="selectedNote.title"
              @input="updateSelectedNoteTitle($event)" />
            <div class="header-actions">                <button class="action-button save-button" @click="openSaveModal">
                <img src="/icons/save-icon.svg" class="action-icon" alt="" />
                <span class="action-text">Save to</span>
              </button><div class="dropdown-container">
                <button class="action-button export-button" @click="openExportModal">
                  <img src="/icons/export-icon.svg" class="action-icon" alt="" />
                  <span class="action-text">Export</span>
                </button>
              </div>
              <button class="action-button import-button" @click="importNote">
                <img src="/icons/import-icon.svg" class="action-icon" alt="" />
                <span class="action-text">Import</span>
              </button>
            </div>
          </div>
          <div class="header-divider"></div>
        </header>

        <!-- NoteEditor without the header -->
        <NoteEditor
          v-if="selectedNote"
          :key="selectedNote.id"
          ref="noteEditorRef"
          :note="selectedNote"
          :last-save-time="lastSaveTime"
          @save="saveNote"
          @update="updateSelectedNote"
          @show-font-modal="handleShowFontModal"
          @show-color-modal="handleShowColorModal"
        />

        <div v-else class="no-note-selected">
          <div class="placeholder-content">
            <p>Select a note or create a new one to start editing</p>
            <button class="create-note-btn" @click="createNewNote">Create New Note</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Delete Single Note Modal -->
    <teleport to="body">
      <div v-if="showDeleteModal" class="modal-overlay">
        <DeleteNoteModal :noteTitle="selectedNote?.title || 'Untitled Note'" @close="showDeleteModal = false"
          @cancel="showDeleteModal = false" @delete="deleteNote" />
      </div>
    </teleport>    <!-- Delete Multiple Notes Modal -->
    <teleport to="body">
      <div v-if="showMultiDeleteModal" class="modal-overlay">
        <DeleteNotesModal
          :notesCount="selectedNotes.length"
          :noteTitles="selectedNotes.map(note => note.title)"
          @close="showMultiDeleteModal = false"
          @cancel="showMultiDeleteModal = false"
          @delete="deleteMultipleNotes" />
      </div>
    </teleport>    <!-- Export Modal -->
    <teleport to="body">
      <div v-if="showExportModal" class="modal-overlay">
        <ExportNoteModal
          v-if="selectedNotes.length <= 1 && selectedNote?.id"
          :note-id="selectedNote.id"
          :note-title="selectedNote?.title || 'Untitled Note'"
          @close="showExportModal = false"
          @export-start="handleExportStart"
          @export-complete="handleExportComplete" />
        <ExportMultipleItemsModal
          v-else
          :items="itemsToExport"
          @close="showExportModal = false"
          @export-start="handleExportStart"
          @export-complete="handleExportComplete" />
      </div>
    </teleport>

    <!-- Export Progress Overlay -->
    <teleport to="body">
      <div v-if="isExportingFile" class="export-progress-overlay">
        <div class="export-progress-modal">
          <div class="export-progress-content">
            <div class="export-spinner"></div>
            <h3 class="export-progress-title">{{ exportProgressTitle }}</h3>
            <p class="export-progress-message">{{ exportProgressMessage }}</p>
            <div class="export-progress-details" v-if="exportProgressDetails">
              <small>{{ exportProgressDetails }}</small>
            </div>
          </div>
        </div>
      </div>
    </teleport>    <!-- Save Modal - Hidden for now until folder system is implemented -->
    <teleport to="body">
      <div v-if="showSaveModal" class="modal-overlay">
        <SaveNotesToFolderModal
          :notes="notesToSave"
          :folders="folders"
          :currentFolderId="selectedFolder !== 'all' ? Number(selectedFolder) : null"
          @confirm="saveNotesWithFolder"
          @cancel="showSaveModal = false" />
      </div>
    </teleport>

    <!-- Font Selection Modal -->
    <teleport to="body">
      <FontSelectionModal
        v-if="showFontModal"
        :current-font="noteEditorRef?.currentFont || 'Montserrat'"
        @close="showFontModal = false"
        @apply-font="handleFontApply"
      />
    </teleport>

    <!-- Color Selection Modal -->
    <teleport to="body">
      <ColorSelectionModal
        v-if="showColorModal"
        :current-color="noteEditorRef?.currentColor || '#000000'"
        @close="showColorModal = false"
        @apply-color="handleColorApply"
      />
    </teleport>

    <!-- Hidden file input for import -->
    <input
      type="file"
      ref="fileInput"
      @change="handleFileImport"
      style="display: none;"
      accept=".md,.noti,.txt"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import DOMPurify from 'dompurify';
import NoteCard from '../components/notes/NoteCard.vue';
import NoteEditor from '../components/notes/NoteEditor.vue';
import DeleteNoteModal from '../components/modals/DeleteNoteModal.vue';
import DeleteNotesModal from '../components/modals/DeleteNotesModal.vue';
import ExportMultipleItemsModal from '../components/modals/ExportMultipleItemsModal.vue';
import ExportNoteModal from '../components/modals/ExportNoteModal.vue';
import SaveNoteModal from '../components/modals/SaveNoteModal.vue';
import SaveNotesToFolderModal from '../components/modals/SaveNotesToFolderModal.vue';
import FontSelectionModal from '../components/modals/FontSelectionModal.vue';
import ColorSelectionModal from '../components/modals/ColorSelectionModal.vue';

// Import interfaces from our type declarations
import type { Note, Folder } from '../types/electron-api';
// Use the exposed electron API from contextBridge
import { useElectronAPI } from '../useElectronAPI';
import { useDiscordActivity } from '../composables/useDiscordActivity';
import { useNotesKeybinds } from '../composables/useNotesKeybinds';
import { usePageLoadMonitoring } from '../composables/usePageLoadMonitoring';
import { useNotesViewStore } from '../stores/notesViewStore';

export default defineComponent({
  name: 'NotesView',  components: {
    NoteCard,
    NoteEditor,
    DeleteNoteModal,
    DeleteNotesModal,
    ExportMultipleItemsModal,
    ExportNoteModal,
    SaveNoteModal,
    SaveNotesToFolderModal,
    FontSelectionModal,
    ColorSelectionModal
  },setup() {
    // Get the API from the composable
    const db = useElectronAPI();
    // Get the route instance to access URL parameters
    const route = useRoute();
    // Get the router instance for navigation
    const router = useRouter();
    // Get Discord activity tracking
    const { setNoteTakingActivity, setBookWritingActivity } = useDiscordActivity();
    
    // Setup keybinds
    const { setupNoteFunctions, activate: activateKeybinds, deactivate: deactivateKeybinds } = useNotesKeybinds();
    
    // Get notes view store for state persistence
    const notesViewStore = useNotesViewStore();

    // Page load monitoring
    const { autoRecordPageMounted } = usePageLoadMonitoring();
    autoRecordPageMounted('Notes');

    // State
    const notes = ref<Note[]>([]);
    const folders = ref<Folder[]>([]);
    const selectedNote = ref<Note | null>(null);
    const selectedFolder = ref<string | number>('all');
    const loading = ref(true);
    // Search state
    const searchQuery = ref<string>('');
    const originalNotes = ref<Note[]>([]);

    // Change detection state
    const originalNoteState = ref<Note | null>(null);

    // Auto-save tracking state
    const lastSaveTime = ref<string | null>(null);
      // Multi-select state
    const selectedNotes = ref<Note[]>([]);
    const lastSelectedNoteId = ref<number | null>(null);
      // Export state
    const itemsToExport = ref<Array<{ id: number; type: 'folder' | 'note'; name: string }>>([]);

    // Save to folder state
    const notesToSave = ref<Note[]>([]);

    // Dropdown state
    const dropdownOpen = ref(false);
    const dropdownContainer = ref<HTMLElement | null>(null);
    const dropdownOptions = ref<HTMLElement | null>(null);    // Modal states
    const showDeleteModal = ref(false);
    const showMultiDeleteModal = ref(false);
    const showExportModal = ref(false);
    const showSaveModal = ref(false);
    const showFontModal = ref(false);
    const showColorModal = ref(false);
    const noteEditorRef = ref<any>(null);

    // Export progress states
    const isExportingFile = ref(false);
    const exportProgressTitle = ref('');
    const exportProgressMessage = ref('');
    const exportProgressDetails = ref('');

    // File input ref for import functionality
    const fileInput = ref<HTMLInputElement | null>(null);    // Computed properties
    const filteredNotes = computed(() => {
      let result = selectedFolder.value === 'all'
        ? notes.value
        : notes.value.filter(note => note.folder_id === selectedFolder.value);

      // Apply search filter if there's a query
      if (searchQuery.value.trim()) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(note =>
          note.title.toLowerCase().includes(query) ||
          (note.content && note.content.toLowerCase().includes(query))
        );
      }

      return result;
    });

    // Click outside to close dropdown
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownContainer.value && !dropdownContainer.value.contains(event.target as Node)) {
        dropdownOpen.value = false;
      }
    };

    // Toggle dropdown open/closed
    const toggleDropdown = () => {
      dropdownOpen.value = !dropdownOpen.value;
    };

    // Get the name of the selected folder
    const getSelectedFolderName = () => {
      if (selectedFolder.value === 'all') {
        return 'All Notes';
      }

      const folder = folders.value.find(f => f.id === selectedFolder.value);
      return folder ? folder.name : 'All Notes';
    };

    // Handle selecting a folder from the dropdown
    const selectFolderOption = (folderId: string | number) => {
      selectedFolder.value = folderId;
      dropdownOpen.value = false;
    };

    // Scroll handler for dropdown
    const handleScroll = (event: WheelEvent) => {
      if (!dropdownOptions.value) return;

      // Don't prevent default for fast scrolling
      const isFastScroll = Math.abs(event.deltaY) > 50;

      if (!isFastScroll) {
        // For slow scrolls, we still want controlled scrolling
        event.preventDefault();

        // For gentle scrolls, move exactly one item
        const optionHeight = 46;
        dropdownOptions.value.scrollBy({
          top: Math.sign(event.deltaY) * optionHeight,
          behavior: 'smooth'
        });
      }
      // For fast scrolls, let the native scrolling happen (much faster)
    };

    // Using the dbApi imported at the top of the file
    // Method to fetch notes from the database API
    const fetchNotes = async (): Promise<Note[]> => {
      try {
        // Use our API layer that will use real or mock implementation as needed
        return await db.notes.getAll();
      } catch (error) {
        console.error('Error fetching notes:', error);
        return [];
      }
    };

    // Method to fetch folders from the database API
    const fetchFolders = async (): Promise<Folder[]> => {
      try {
        // Use our API layer that will use real or mock implementation as needed
        return await db.folders.getAll();
      } catch (error) {
        console.error('Error fetching folders:', error);
        return [];
      }
    };    const loadNotes = async () => {
      loading.value = true;

      try {
        // Fetch notes from the database
        const fetchedNotes = await fetchNotes();
        notes.value = fetchedNotes;
        originalNotes.value = fetchedNotes; // Store original copy for search reset
        loading.value = false;

        // Check for restoration data first
        const restorationData = notesViewStore.getRestorationData();

        if (restorationData) {
          console.log('🔄 Restoring NotesView state from store');

          // Restore search state
          if (restorationData.searchQuery) {
            searchQuery.value = restorationData.searchQuery;
          }

          // Restore folder selection
          selectedFolder.value = restorationData.selectedFolderId;

          // Restore note selection
          if (restorationData.selectedNoteId) {
            const noteToRestore = notes.value.find(note => note.id === restorationData.selectedNoteId);
            if (noteToRestore) {
              selectNote(noteToRestore);
              
              // Restore multi-selection if applicable
              if (restorationData.selectedNoteIds.length > 0) {
                selectedNotes.value = notes.value.filter(note =>
                  restorationData.selectedNoteIds.includes(note.id!)
                );
                lastSelectedNoteId.value = restorationData.lastSelectedNoteId;
              }
              
              // Schedule scroll restoration
              restoreScrollPositions(restorationData);
              
              // Schedule editor state restoration
              setTimeout(() => {
                if (noteEditorRef.value?.restoreEditorState) {
                  noteEditorRef.value.restoreEditorState();
                }
              }, 300);
              
              // Mark store as initialized after successful restoration
              notesViewStore.markInitialized();
              return;
            }
          }
        }

        // Fallback to existing logic (URL params or first note)
        const noteIdFromQuery = route.query.noteId;
        if (noteIdFromQuery && typeof noteIdFromQuery === 'string') {
          const noteId = parseInt(noteIdFromQuery, 10);
          const noteToSelect = notes.value.find(note => note.id === noteId);
          if (noteToSelect) {
            selectNote(noteToSelect);
            notesViewStore.markInitialized();
            return;
          }
        }

        // Select the first note if any exist
        if (notes.value.length > 0) {
          selectNote(notes.value[0]);
        }
        
        notesViewStore.markInitialized();
      } catch (error) {
        console.error('Error loading notes:', error);
        loading.value = false;
      }
    };
      // Search functionality
    const filterNotes = () => {
      // The computed property will handle the filtering
      console.log('Filtering notes with query:', searchQuery.value);
    };

    const clearSearch = () => {
      searchQuery.value = '';
    };

    const focusSearch = (event: MouseEvent) => {
      // Prevent focus if clicking on the clear button
      if (searchInputRef.value && !(event.target as HTMLElement).closest('.clear-search')) {
        searchInputRef.value.focus();
      }
    };    // Function to load a specific note by ID
    const loadNoteById = async (noteId: number) => {
      try {
        console.log(`Loading note by ID: ${noteId}`);
        const note = await db.notes.getById(noteId);

        if (note) {
          console.log(`Note found, selecting: ${note.title}`);
          selectNote(note);

          // If we're filtering by folder, make sure the folder matches the note's folder
          if (selectedFolder.value !== 'all' && note.folder_id !== null && selectedFolder.value !== note.folder_id) {
            // Update the folder filter to match the note's folder
            if (note.folder_id !== undefined) {
              selectedFolder.value = note.folder_id;
            }
          }
        } else {
          console.error(`Note with ID ${noteId} not found`);
        }
      } catch (error) {
        console.error(`Error loading note by ID ${noteId}:`, error);
      }
    };

    const loadFolders = async () => {
      try {
        // Fetch folders from the database
        folders.value = await fetchFolders();
      } catch (error) {
        console.error('Error loading folders:', error);
      }
    };

    // Function to check if the current note has changes compared to the original state
    const hasNoteChanged = (): boolean => {
      if (!selectedNote.value || !originalNoteState.value) {
        return false;
      }

      // Compare the key fields that matter for saving
      return (
        selectedNote.value.title !== originalNoteState.value.title ||
        selectedNote.value.content !== originalNoteState.value.content ||
        selectedNote.value.html_content !== originalNoteState.value.html_content ||
        selectedNote.value.folder_id !== originalNoteState.value.folder_id ||
        selectedNote.value.book_id !== originalNoteState.value.book_id
      );
    };

    const selectNote = async (note: Note) => {
      selectedNote.value = { ...note };
      // Store the original state for change detection
      originalNoteState.value = { ...note };

      // Add this note to recent items if it has an ID
      if (note.id) {
        db.recentItems.addNote(note.id).catch((err: any) => {
          console.error('Error adding note to recent items:', err);
        });
      }

      // Update Discord activity - check if note is linked to a book
      if (note.book_id) {
        try {
          const book = await db.books.getById(note.book_id);
          setBookWritingActivity(book?.title);
        } catch (error) {
          console.error('Error fetching book for Discord activity:', error);
          setNoteTakingActivity();
        }
      } else {
        setNoteTakingActivity();
      }
    };

    const createNewNote = async () => {
      try {
        // Create a new empty note
        const newNote: Note = {
          title: 'Untitled Note',
          content: '',
          html_content: '<p></p>',
          folder_id: selectedFolder.value !== 'all' ? Number(selectedFolder.value) : null
        };

        // Create in the database (real or mock)
        const createdNote = await db.notes.create(newNote);

        // Add to notes array and select it
        notes.value.unshift(createdNote);
        selectNote(createdNote);

        // Scroll to the newly created note
        setTimeout(() => {
          const notesList = document.querySelector('.notes-list');
          if (notesList) {
            notesList.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }
        }, 50);
      } catch (error: any) {
        console.error('Error creating new note:', error);
      }
    };

    const saveNote = async (note: Note, forceUpdate: boolean = false) => {
      // Validate input
      if (!note) {
        console.error('Cannot save: note is undefined');
        return;
      }

      console.log('Saving note:', note.id, note.title);

      try {
        if (note.id) {
          // Update existing note
          const updatedNote = await db.notes.update(note.id, note);
          console.log('Note updated:', updatedNote.id);

          // Update our local state
          const index = notes.value.findIndex(n => n.id === note.id);
          if (index !== -1) {
            notes.value[index] = updatedNote;
            selectedNote.value = { ...updatedNote };
            // Update the original state after successful save
            originalNoteState.value = { ...updatedNote };
          }
        } else {
          // Create a new note
          const newNote = await db.notes.create(note);
          console.log('New note created:', newNote.id);

          // Add to local state
          notes.value.unshift(newNote);
          selectedNote.value = { ...newNote };
          // Set the original state for the new note
          originalNoteState.value = { ...newNote };
        }

        // Update last save time
        lastSaveTime.value = new Date().toISOString();
      } catch (error: any) {
        console.error('Error saving note:', error);
      }
    };

    // Save note with change detection
    const saveNoteWithChangeDetection = async (note: Note): Promise<boolean> => {
      // Check if there are actually changes to save
      if (!hasNoteChanged()) {
        console.log('No changes detected, skipping save operation');
        return false; // No changes to save
      }

      await saveNote(note);
      return true; // Changes were saved
    };
      const saveNoteWithFolder = (folderId: number | null) => {
      if (!selectedNote.value) return;

      // Update the note with the new folder ID
      const updatedNote = {
        ...selectedNote.value,
        folder_id: folderId,
        updated_at: new Date().toISOString()
      };

      saveNote(updatedNote);
      showSaveModal.value = false;
    };

    // Save multiple notes to the selected folder
    const saveNotesWithFolder = async (folderId: number | null) => {
      if (notesToSave.value.length === 0) {
        showSaveModal.value = false;
        return;
      }

      try {
        // Create a copy of notes to save
        const notes = [...notesToSave.value];

        // Get target folder details to inherit book_id if applicable
        let targetBookId: number | null = null;
        if (folderId !== null) {
          try {
            const targetFolder = await db.folders.getById(folderId);
            targetBookId = targetFolder.book_id || null;
          } catch (error) {
            console.warn(`Could not fetch folder details for ID ${folderId}:`, error);
            // Continue with null book_id if folder fetch fails
          }
        }

        for (const note of notes) {
          // Update the note with the new folder ID and inherited book_id
          const updatedNote = {
            ...note,
            folder_id: folderId,
            book_id: targetBookId, // Inherit book_id from target folder
            updated_at: new Date().toISOString()
          };

          if (updatedNote.id) {
            // Save the note
            await saveNote(updatedNote);
          }
        }

        // Success message
        console.log(`Saved ${notes.length} note(s) to folder ID: ${folderId} with book_id: ${targetBookId}`);
      } catch (error: any) {
        console.error('Error saving notes to folder:', error);
      }

      // Close the modal
      showSaveModal.value = false;
    };

    // Open the save modal for single or multiple notes
    const openSaveModal = () => {
      if (selectedNotes.value.length > 0) {
        // Save multiple selected notes
        notesToSave.value = [...selectedNotes.value];
      } else if (selectedNote.value) {
        // Save single note
        notesToSave.value = [selectedNote.value];
      } else {
        return; // Nothing to save
      }

      showSaveModal.value = true;
    };

    const updateSelectedNote = async (updates: Partial<Note>) => {
      if (!selectedNote.value) return;

      // Update the selected note with the provided updates
      const updatedNote = { ...selectedNote.value, ...updates };
      selectedNote.value = updatedNote;

      // Update Discord activity when user is actively editing
      if (updatedNote.book_id) {
        try {
          const book = await db.books.getById(updatedNote.book_id);
          setBookWritingActivity(book?.title);
        } catch (error) {
          console.error('Error fetching book for Discord activity:', error);
          setNoteTakingActivity();
        }
      } else {
        setNoteTakingActivity();
      }

      // Auto-save changes to the database after a short debounce
      if (updatedNote.id) {
        // Clear any existing timeout
        if (window.autoSaveTimeout) {
          clearTimeout(window.autoSaveTimeout);
        }

        // Set a new timeout to save after 1 second of inactivity
        window.autoSaveTimeout = setTimeout(() => {
          // Use change detection for auto-save as well
          saveNoteWithChangeDetection(updatedNote).then((wasSaved) => {
            if (wasSaved) {
              console.log('Auto-saved note changes');
            } else {
              console.log('Auto-save skipped - no changes detected');
            }
          }).catch((error) => {
            console.error('Error during auto-save:', error);
          });
        }, 1000);
      }
    };

    const updateSelectedNoteTitle = (event: Event) => {
      if (!selectedNote.value) return;

      const target = event.target as HTMLInputElement;
      updateSelectedNote({ title: target.value });
    };

    const deleteNote = async () => {
      if (!selectedNote.value || !selectedNote.value.id) {
        showDeleteModal.value = false;
        return;
      }

      try {
        const noteId = selectedNote.value.id;

        // Delete from database
        await db.notes.delete(noteId);

        // Delete the note from our array
        const index = notes.value.findIndex(n => n.id === noteId);
        if (index !== -1) {
          notes.value.splice(index, 1);
        }

        // Clear the selected note or select the first available note
        selectedNote.value = notes.value.length > 0 ? { ...notes.value[0] } : null;
      } catch (error: any) {
        console.error('Error deleting note:', error);
      }

      // Close the dialog
      showDeleteModal.value = false;
    };

    const importNote = () => {
      // Trigger the file input to open the file manager
      if (fileInput.value) {
        fileInput.value.click();
      }
    };

    const handleFileImport = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      const file = target.files[0];

      // Validate file type
      const allowedExtensions = ['.md', '.noti', '.txt'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

      if (!allowedExtensions.includes(fileExtension)) {
        alert(`Invalid file type. Only ${allowedExtensions.join(', ')} files are supported.`);
        // Reset file input
        if (fileInput.value) {
          fileInput.value.value = '';
        }
        return;
      }

      // Handle different file types

      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const content = e.target?.result as string;
          let title = file.name.replace(/\.[^/.]+$/, ""); // Use filename without extension as title

          let noteContent = content;
          let htmlContent = `<p>${DOMPurify.sanitize(content)}</p>`; // Sanitized conversion

          // Handle different file formats
          if (fileExtension === '.noti') {
            try {
              // Parse .noti file as JSON
              const notiData = JSON.parse(content);
              
              // Handle new .noti format (v1.0)
              if (notiData.version === "1.0" && notiData.type === "noti-note") {
                // Extract content from new format
                if (notiData.content) {
                  noteContent = notiData.content.markdown || notiData.content.plain_text || '';
                  htmlContent = notiData.content.html || `<p>${DOMPurify.sanitize(noteContent)}</p>`;
                }
                // Use title from metadata if available
                if (notiData.metadata && notiData.metadata.title) {
                  title = notiData.metadata.title;
                }
              } else {
                // Fallback for old format (if any)
                if (notiData.content) {
                  noteContent = notiData.content;
                  htmlContent = notiData.html_content
                    ? DOMPurify.sanitize(notiData.html_content)
                    : `<p>${DOMPurify.sanitize(notiData.content)}</p>`;
                }
              }
            } catch (parseError) {
              console.warn('Failed to parse .noti file as JSON, treating as plain text');
            }
          }

          // Use the backend import API for proper processing
          const createdNote = await db.notes.import(content, fileExtension.substring(1), title);

          // Add to notes array and select it
          notes.value.unshift(createdNote);
          selectNote(createdNote);

        } catch (error: any) {
          console.error('Error importing note:', error);
          alert('Error importing note. Check console for details.');
        } finally {
          // Reset file input for future imports
          if (fileInput.value) {
            fileInput.value.value = '';
          }
        }
      };

      // Read the file as text
      reader.readAsText(file);
    };

    // Folder creation is temporarily disabled until folder system is fully implemented
    const createNewFolder = async () => {
      console.log('Folder creation is not yet implemented');
      alert('Creating folders will be available in a future update.');
    };

    const formatDate = (dateString?: string): string => {
      if (!dateString) return '';

      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    };

    const getFolderPath = (folderId: number): string => {
      const folder = folders.value.find(f => f.id === folderId);
      if (!folder) return 'Unknown';

      // In a real implementation, you would recursively build the path
      // For now, just return the folder name
      return folder.name;
    };

    // Handle trash button click to delete single or multiple notes
    const handleTrashButtonClick = () => {
      // If we have multiple notes selected, show the multi-delete modal
      if (selectedNotes.value.length > 0) {
        showMultiDeleteModal.value = true;
      } else if (selectedNote.value) {
        // Otherwise show the single delete modal
        showDeleteModal.value = true;
      }
    };    // Handle multi-selection of notes
    const handleMultiSelect = (note: Note, event: { ctrlKey: boolean, shiftKey: boolean }) => {
      console.log('Multi-select triggered:', {
        noteId: note.id,
        ctrlKey: event.ctrlKey,
        shiftKey: event.shiftKey,
        currentSelection: selectedNotes.value.map(n => n.id)
      });

      // Check if the note is already selected
      const isAlreadySelected = selectedNotes.value.some(n => n.id === note.id);

      if (event.shiftKey && lastSelectedNoteId.value !== null) {
        // Handle shift-click to select a range
        const lastIndex = filteredNotes.value.findIndex(n => n.id === lastSelectedNoteId.value);
        const currentIndex = filteredNotes.value.findIndex(n => n.id === note.id);

        if (lastIndex !== -1 && currentIndex !== -1) {
          // Determine range start and end
          const start = Math.min(lastIndex, currentIndex);
          const end = Math.max(lastIndex, currentIndex);

          // Get all notes in the range
          const notesToSelect = filteredNotes.value.slice(start, end + 1);

          // If Ctrl key is also pressed, add to existing selection
          // Otherwise, replace the selection
          const baseSelection = event.ctrlKey ? [...selectedNotes.value] : [];
          const newSelection = [...baseSelection];

          // Add notes from the range that aren't already in the selection
          notesToSelect.forEach(rangeNote => {
            if (!newSelection.some(n => n.id === rangeNote.id)) {
              newSelection.push(rangeNote);
            }
          });

          // Update the selection
          selectedNotes.value = newSelection;
        }
      } else if (event.ctrlKey) {
        // Handle Ctrl+click to toggle individual selection
        if (isAlreadySelected) {
          // Remove from selection
          selectedNotes.value = selectedNotes.value.filter(n => n.id !== note.id);
        } else {
          // Add to selection
          selectedNotes.value = [...selectedNotes.value, note];
        }
      } else {
        // Regular click - replace selection with just this note
        selectedNotes.value = [note];
      }

      // Update last selected note ID for future shift+click operations
      lastSelectedNoteId.value = note.id || null;

      // Also update the selectedNote for the editor, but only if there's exactly one note selected
      if (selectedNotes.value.length === 1) {
        selectNote(selectedNotes.value[0]);
      }
    };// Handle right-click on a note
    const handleRightClick = (note: Note, event: MouseEvent) => {
      // If the note isn't already selected, select it first
      if (!selectedNotes.value.some(n => n.id === note.id)) {
        // Add to selection
        selectedNotes.value.push(note);
        lastSelectedNoteId.value = note.id || null;
        // Also update the current selection
        selectNote(note);
      }

      // You could implement a context menu here if needed
    };

    // Handle click on the notes list container to clear selection
    const handleNotesListClick = (event: MouseEvent) => {
      // Only handle direct clicks on the container (not bubbled events)
      if (selectedNotes.value.length > 0) {
        console.log('Notes list container clicked - clearing selection');

        // Clear multi-selection
        selectedNotes.value = [];
        lastSelectedNoteId.value = null;

        // Keep current editor note if there is one
        if (!selectedNote.value && notes.value.length > 0) {
          selectNote(notes.value[0]);
        }
      }
    };

    // Show delete modal for single note
    const showSingleDeleteModal = () => {
      if (selectedNote.value) {
        showDeleteModal.value = true;
      }
    };
      // Function removed because it was causing errors
      // Delete multiple notes
    const deleteMultipleNotes = async () => {
      if (selectedNotes.value.length === 0) {
        showMultiDeleteModal.value = false;
        return;
      }      try {
        // Create a copy to avoid modification during iteration
        const notesToDelete = [...selectedNotes.value];

        for (const note of notesToDelete) {
          if (note.id) {
            // Delete from database
            await db.notes.delete(note.id);

            // Delete from our array
            const index = notes.value.findIndex(n => n.id === note.id);
            if (index !== -1) {
              notes.value.splice(index, 1);
            }
          }
        }

        // Clear selected notes
        selectedNotes.value = [];

        // Clear or update the selected note
        selectedNote.value = notes.value.length > 0 ? { ...notes.value[0] } : null;
      } catch (error: any) {
        console.error('Error deleting notes:', error);
      }

      // Close the dialog
      showMultiDeleteModal.value = false;
    };
      // Reference to the search input element
    const searchInputRef = ref<HTMLInputElement | null>(null);
      // Keyboard shortcut handler for Ctrl+S, Ctrl+F, and Escape
    const handleKeyboardShortcuts = (event: KeyboardEvent) => {
      try {
        // Debug info
        console.log('Key pressed:', event.key,
                    'Ctrl:', event.ctrlKey,
                    'Meta:', event.metaKey,
                    'selectedNote:', selectedNote.value ? selectedNote.value.id : 'none');

        // Check if Ctrl+S was pressed
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
          event.preventDefault(); // Prevent browser's save dialog

          // Save the current note if one is selected
          if (selectedNote.value) {
            // Make a copy of the note to avoid any reactivity issues
            const noteToSave = { ...selectedNote.value };

            // Use change detection to avoid unnecessary saves
            saveNoteWithChangeDetection(noteToSave).then((wasSaved) => {
              if (wasSaved) {
                console.log('Note saved via keyboard shortcut (Ctrl+S)');
              } else {
                console.log('No changes to save (Ctrl+S pressed but note unchanged)');
              }
            }).catch((error) => {
              console.error('Error saving note via Ctrl+S:', error);
            });
          } else {
            console.log('No note selected to save');
          }
        }

        // Check if Ctrl+F was pressed to focus the search input
        if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
          event.preventDefault(); // Prevent browser's find function

          // Focus the search input
          if (searchInputRef.value) {
            searchInputRef.value.focus();
            console.log('Search input focused via keyboard shortcut (Ctrl+F)');
          }
        }

        // Check if Escape was pressed to clear multi-selection
        if (event.key === 'Escape' && selectedNotes.value.length > 0) {
          console.log('Escape key pressed - clearing selection');
          selectedNotes.value = [];
          lastSelectedNoteId.value = null;

          // If there's a single selected note, keep it selected for the editor
          if (selectedNote.value) {
            console.log('Keeping current note selected for editor');
          }
        }
      } catch (error) {
        console.error('Error in keyboard shortcut handler:', error);
      }
    };

    // Functions for handling export
    const prepareExportItems = () => {
      if (selectedNotes.value.length > 0) {
        // Export multiple selected notes
        return selectedNotes.value
          .filter(note => note.id !== undefined)
          .map(note => ({
            id: note.id as number,
            type: 'note' as const,
            name: note.title || 'Untitled Note'
          }));
      } else if (selectedNote.value && selectedNote.value.id) {
        // Export single note
        return [{
          id: selectedNote.value.id,
          type: 'note' as const,
          name: selectedNote.value.title || 'Untitled Note'
        }];
      }
      return [];
    };

    // Handle export start event for multiple items
    const handleExportStart = (data: { format: string; itemCount: number; itemType: string }) => {
      isExportingFile.value = true;
      exportProgressTitle.value = `Exporting ${data.itemType}`;
      exportProgressMessage.value = `Preparing ${data.format.toUpperCase()} export...`;
      exportProgressDetails.value = `${data.itemCount} item${data.itemCount > 1 ? 's' : ''} selected`;
    };

    // Handle export complete event
    const handleExportComplete = (result: string | { success: boolean; error?: string; message?: string }) => {
      console.log('Export completed:', result);

      // Hide progress overlay
      isExportingFile.value = false;
      exportProgressTitle.value = '';
      exportProgressMessage.value = '';
      exportProgressDetails.value = '';

      // Handle the result
      if (typeof result === 'object') {
        if (result.success) {
          // For multiple notes export (from ExportMultipleItemsModal)
          console.log('Export successful:', result.message);
        } else {
          // Handle export error
          console.error('Export failed:', result.error);
          alert(`Export failed: ${result.error || 'Unknown error'}`);
        }
      } else if (typeof result === 'string') {
        // For single note export (from ExportNoteModal)
        console.log('Export successful:', result);
        // Show success notification to user
        if (result.includes('Success:')) {
          alert(result);
        }
      }

      showExportModal.value = false;
    };

    // Open export modal with prepared items
    const openExportModal = () => {
      itemsToExport.value = prepareExportItems();
      
      // Check if there are valid items to export
      if (itemsToExport.value.length === 0) {
        alert('No valid notes selected for export. Please select a saved note.');
        return;
      }
      
      showExportModal.value = true;
    };

    // Configure keybind functions
    setupNoteFunctions({
      createNewNote,
      saveCurrentNote: async () => {
        if (selectedNote.value) {
          try {
            await saveNoteWithChangeDetection(selectedNote.value);
          } catch (error) {
            console.error('Failed to save note:', error);
            // Note: Error notification is already handled within saveNoteWithChangeDetection
          }
        }
      },
      focusSearchBar: () => {
        if (searchInputRef.value) {
          searchInputRef.value.focus();
        }
      },
      deleteSelectedNotes: handleTrashButtonClick,
      importNote,
      selectAllNotes: () => {
        selectedNotes.value = [...filteredNotes.value];
      },
      navigateNoteList: (direction) => {
        // Basic implementation - you can enhance this
        const currentIndex = filteredNotes.value.findIndex(note => note.id === selectedNote.value?.id);
        if (currentIndex !== -1) {
          const newIndex = direction === 'up' ? Math.max(0, currentIndex - 1) : Math.min(filteredNotes.value.length - 1, currentIndex + 1);
          if (filteredNotes.value[newIndex]) {
            selectNote(filteredNotes.value[newIndex]);
          }
        }
      },
      openSelectedNote: () => {
        if (selectedNotes.value.length === 1) {
          selectNote(selectedNotes.value[0]);
        }
      }
    });

    // Handle action from dashboard
    const handleCreateAction = async () => {
      try {
        // Clean URL first
        await router.replace({ query: { ...route.query, action: undefined } });
        
        // Create note after data is loaded
        if (loading.value) {
          // Wait for data to load
          await new Promise<void>(resolve => {
            const unwatch = watch(() => loading.value, (isLoading) => {
              if (!isLoading) {
                unwatch();
                resolve();
              }
            });
          });
        }
        
        await createNewNote();
      } catch (error) {
        console.error('Failed to create note from dashboard action:', error);
      }
    };

    // Watch selectedNote changes to save state
    watch(() => selectedNote.value, (newNote) => {
      if (newNote?.id) {
        notesViewStore.saveNoteSelection(newNote.id, selectedFolder.value);
      }
    });

    // Watch search query changes
    watch(() => searchQuery.value, (newQuery) => {
      notesViewStore.saveSearchState(newQuery);
    });

    // Watch folder selection changes
    watch(() => selectedFolder.value, (newFolder) => {
      if (selectedNote.value?.id) {
        notesViewStore.saveNoteSelection(selectedNote.value.id, newFolder);
      }
    });

    // Watch multi-selection changes
    watch(() => selectedNotes.value, (newSelection) => {
      const noteIds = newSelection.map(note => note.id!).filter(id => id !== undefined);
      notesViewStore.saveMultiSelection(noteIds, lastSelectedNoteId.value);
    }, { deep: true });

    // Save notes list scroll position with debouncing
    let scrollSaveTimeout: ReturnType<typeof setTimeout> | null = null;
    const saveNotesListScrollPosition = () => {
      const notesList = document.querySelector('.notes-list');
      if (notesList) {
        if (scrollSaveTimeout) clearTimeout(scrollSaveTimeout);
        scrollSaveTimeout = setTimeout(() => {
          notesViewStore.saveNotesListScroll(notesList.scrollTop);
        }, 300);
      }
    };

    // Restore scroll positions after state restoration
    const restoreScrollPositions = async (restorationData: any) => {
      // Wait for next tick to ensure DOM is ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Restore notes list scroll position
      if (restorationData.notesListScrollTop > 0) {
        const notesList = document.querySelector('.notes-list');
        if (notesList) {
          notesList.scrollTop = restorationData.notesListScrollTop;
        }
      }
    };

    // Lifecycle hooks
    onMounted(() => {
      // Check if we need to create a note first
      const actionParam = route.query.action;
      const shouldCreateNote = actionParam === 'create';
      
      loadNotes().then(() => {
        // Check if there's a note ID in the URL query
        const noteIdParam = route.query.noteId;
        if (noteIdParam) {
          const noteId = parseInt(noteIdParam as string, 10);
          if (!isNaN(noteId)) {
            // Load the specific note
            loadNoteById(noteId);
          }
        }
        
        // Handle action parameter - create note AFTER loading is done
        if (shouldCreateNote) {
          // Clear the URL first to prevent re-triggering
          router.replace({ query: { ...route.query, action: undefined } }).then(() => {
            createNewNote();
          });
        }
      });

      loadFolders();

      // Activate keybinds for this view
      activateKeybinds();

      // Add event listeners
      document.addEventListener('click', handleClickOutside);
      document.addEventListener('keydown', handleKeyboardShortcuts);
      
      // Add scroll listener for notes list
      const notesList = document.querySelector('.notes-list');
      if (notesList) {
        notesList.addEventListener('scroll', saveNotesListScrollPosition);
      }
    });

    // Watch for route changes to handle navigation to specific notes
    watch(() => route.query.noteId, (newNoteId) => {
      if (newNoteId && typeof newNoteId === 'string') {
        const noteId = parseInt(newNoteId, 10);
        if (!isNaN(noteId)) {
          loadNoteById(noteId);
        }
      }
    });
    
    // Watch for action parameter changes
    watch(() => route.query.action, async (action) => {
      if (action === 'create' && !loading.value) {
        await handleCreateAction();
      }
    });

    // Modal handler functions
    const handleShowFontModal = () => {
      showFontModal.value = true;
    };

    const handleShowColorModal = () => {
      showColorModal.value = true;
    };

    const handleFontApply = (fontFamily: string) => {
      // Call NoteEditor method via ref
      if (noteEditorRef.value?.applyFontFamily) {
        noteEditorRef.value.applyFontFamily(fontFamily);
      }
      showFontModal.value = false;
    };

    const handleColorApply = (color: string) => {
      // Call NoteEditor method via ref
      if (noteEditorRef.value?.applyTextColor) {
        noteEditorRef.value.applyTextColor(color);
      }
      showColorModal.value = false;
    };

    const handleColorRemove = () => {
      // Call NoteEditor method via ref
      if (noteEditorRef.value?.removeTextColor) {
        noteEditorRef.value.removeTextColor();
      }
      showColorModal.value = false;
    };

    onBeforeUnmount(() => {
      // Deactivate keybinds
      deactivateKeybinds();
      
      // Remove event listeners when component is unmounted
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('keydown', handleKeyboardShortcuts);
      
      // Remove scroll listener
      const notesList = document.querySelector('.notes-list');
      if (notesList) {
        notesList.removeEventListener('scroll', saveNotesListScrollPosition);
      }
      
      // Clear scroll timeout
      if (scrollSaveTimeout) clearTimeout(scrollSaveTimeout);
    });    return {
      notes,
      folders,
      selectedNote,
      selectedFolder,
      filteredNotes,
      loading,
      showDeleteModal,
      showMultiDeleteModal,
      showExportModal,
      showSaveModal,
      showFontModal,
      showColorModal,
      noteEditorRef,
      dropdownOpen,
      dropdownContainer,
      dropdownOptions,      fileInput,
      selectedNotes,
      itemsToExport,
      notesToSave,
      searchQuery,      searchInputRef,
      lastSaveTime,
      isExportingFile,
      exportProgressTitle,
      exportProgressMessage,
      exportProgressDetails,
      filterNotes,
      clearSearch,
      focusSearch,
      toggleDropdown,
      getSelectedFolderName,
      selectFolderOption,
      handleScroll,
      selectNote,
      createNewNote,
      saveNote,
      saveNoteWithFolder,
      saveNotesWithFolder,
      updateSelectedNote,
      updateSelectedNoteTitle,
      deleteNote,
      importNote,
      handleFileImport,
      createNewFolder,
      formatDate,
      getFolderPath,
      handleMultiSelect,
      handleRightClick,
      handleNotesListClick,
      deleteMultipleNotes,
      handleTrashButtonClick,
      loadNoteById,
      prepareExportItems,
      handleExportStart,
      handleExportComplete,
      openExportModal,
      openSaveModal,
      handleShowFontModal,
      handleShowColorModal,
      handleFontApply,
      handleColorApply,
      handleColorRemove
    };
  }
});
</script>

<style scoped>
.notes-view {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  font-family: 'Montserrat', sans-serif;
  /* Prevent text selection by default */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.notes-container {
  display: flex;
  height: 100%;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

/* Notes Sidebar */
.notes-sidebar {
  width: 350px;
  border-right: 1px solid var(--color-border-secondary);
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-primary);
  overflow: hidden;
  position: relative;
}

.sidebar-header {
  padding: 24px;
  border-bottom: 1px solid var(--color-border-primary);
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: var(--color-input-bg);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid var(--color-input-border);
  cursor: text;
}

.search-bar:focus-within {
  background-color: var(--color-bg-secondary);
  border: 1px solid var(--color-input-focus);
}

.search-bar input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-input-text);
  padding: 0 8px;
  user-select: text;
  -webkit-user-select: text;
}

.search-bar input::placeholder {
  color: var(--color-input-placeholder);
}

.search-icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-primary);
  margin-right: 2px;
}

.clear-search {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  width: 14px;
  height: 14px;
  color: var(--color-text-primary);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.close-icon:hover {
  opacity: 1;
}

.new-note-button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  background-color: var(--color-btn-secondary-bg);
  border: none;
  border-radius: 8px;
  color: var(--color-btn-secondary-text);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 16px;
  transition: background-color 0.2s;
}

.new-note-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.plus-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
}

.dropdown {
  flex: 1;
  position: relative;
}

.folder-select {
  width: 100%;
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  cursor: pointer;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.folder-select:focus {
  outline: none;
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px var(--color-nav-item-active);
}

.dropdown-arrow {
  width: 12px;
  height: 12px;
  transition: transform 0.2s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

/* Custom dropdown options styling */
.dropdown-options {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  background: var(--color-modal-bg);
  border-radius: 8px;
  box-shadow: 0 4px 15px var(--color-card-shadow);
  z-index: 100;
  overflow: hidden;
  border: 1px solid var(--color-border-primary);
  max-height: 300px;
  overflow-y: auto;
  scroll-snap-type: y proximity;
}

/* Custom scrollbar styling */
.dropdown-options::-webkit-scrollbar {
  width: 8px;
}

.dropdown-options::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 0 8px 8px 0;
}

.dropdown-options::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.option {
  padding: 14px 16px;
  font-size: 14px;
  color: var(--color-text-primary);
  cursor: pointer;
  border-bottom: 1px solid var(--color-border-primary);
  transition: background-color 0.1s ease;
  height: 46px;
  box-sizing: border-box;
  scroll-snap-align: start;
  display: flex;
  align-items: center;
}

.option:last-child {
  border-bottom: none;
}

.option:hover {
  background-color: var(--color-nav-item-hover);
}

.trash-button {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--color-trash-btn-bg);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.trash-button:hover {
  background-color: var(--color-trash-btn-hover);
}

.trash-button.disabled {
  background-color: var(--color-trash-btn-disabled-bg);
  cursor: not-allowed;
  opacity: 0.7;
}

.trash-icon {
  width: 16px;
  height: 16px;
  filter: invert(21%) sepia(68%) saturate(3598%) hue-rotate(350deg) brightness(89%) contrast(86%);
}

/* Removed multi-select styling */

.notes-list {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  scroll-snap-type: y proximity;
  position: relative;
  z-index: 1;
  isolation: isolate;
}

/* Selection mode styles */
.notes-list.selection-mode {
  background-color: var(--color-bg-tertiary);
}

.notes-list.selection-mode :deep(.note-card:not(.selected):hover) {
  background-color: var(--color-bg-secondary);
  opacity: 0.95;
}

/* Custom scrollbar styling for notes list */
.notes-list::-webkit-scrollbar {
  width: 8px;
}

.notes-list::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
  border-radius: 0 8px 8px 0;
}

.notes-list::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.notes-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Fixed size for note cards */
:deep(.note-card) {
  scroll-snap-align: start;
  height: 120px;
  min-height: 120px;
  max-height: 120px;
  flex-shrink: 0;
}

.empty-state,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-text-secondary);
  text-align: center;
  padding: 40px 20px;
}

/* Editor Section - Enhanced overflow protection */
.editor-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 100%; /* Prevent wrapper overflow */
  min-width: 0; /* Allow flex shrinking */
}

.no-note-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-secondary);
}

.placeholder-content {
  background-color: var(--color-card-bg);
  padding: 40px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 2px 10px var(--color-card-shadow);
  max-width: 400px;
}

.placeholder-content p {
  color: var(--color-text-secondary);
  margin-bottom: 20px;
  font-size: 16px;
}

.create-note-btn {
  padding: 12px 20px;
  background-color: var(--color-btn-primary-bg);
  border: none;
  border-radius: 8px;
  color: var(--color-btn-primary-text);
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.create-note-btn:hover {
  background-color: var(--color-btn-primary-hover);
  transform: translateY(-1px);
}

/* Modal Styling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Added Editor Header Styles */
.editor-header {
  background-color: var(--color-bg-primary);
  display: flex;
  width: 100%;
  padding-top: 22px;
  flex-direction: column;
  align-items: stretch;
  color: var(--color-text-primary);
}

.header-content {
  align-self: center;
  display: flex;
  width: 100%;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px;
}

.note-title-input {
  border-radius: 10px;
  border: 1px solid var(--color-input-border);
  padding: 10px 20px;
  font-size: 24px;
  font-family: inherit;
  color: var(--color-input-text);
  background-color: var(--color-input-bg);
  width: 60%;
  outline: none;
  font-weight: 700;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.note-title-input:focus {
  border-color: var(--color-input-focus);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  position: relative;
}

.action-button {
  border-radius: 8px;
  border: 1px solid var(--color-btn-secondary-border);
  display: flex;
  height: 40px;
  padding: 0 16px;
  align-items: center;
  justify-content: center;
  background: var(--color-btn-secondary-bg);
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s;
  color: var(--color-btn-secondary-text);
  gap: 6px;
}

.action-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.action-button.save-button {
  background-color: var(--color-btn-secondary-bg);
  border: none;
}

.action-button.save-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.action-icon {
  width: 16px;
  height: 16px;
}

.dropdown-container {
  position: relative;
}

.header-divider {
  background-color: var(--color-border-primary);
  height: 1px;
  margin-top: 22px;
  width: 100%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notes-container {
    flex-direction: column;
  }

  .notes-sidebar {
    width: 100%;
    height: 300px;
    min-height: 300px;
    border-right: none;
    border-bottom: 1px solid var(--color-border-primary);
  }

  .editor-wrapper {
    height: calc(100% - 300px);
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .note-title-input {
    width: 100%;
  }

  .header-actions {
    margin-top: 10px;
    justify-content: flex-end;
  }
}

/* Export Progress Overlay Styles */
.export-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-export-progress-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20000;
  backdrop-filter: blur(2px);
}

.export-progress-modal {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  padding: 40px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 40px var(--color-card-shadow);
  text-align: center;
  font-family: 'Montserrat', sans-serif;
}

.export-progress-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.export-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--color-export-progress-spinner-track);
  border-top: 4px solid var(--color-export-progress-spinner-active);
  border-radius: 50%;
  animation: export-spin 1s linear infinite;
}

@keyframes export-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.export-progress-title {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--color-export-progress-title);
}

.export-progress-message {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-export-progress-message);
}

.export-progress-details {
  margin: 0;
  font-size: 14px;
  color: var(--color-export-progress-details);
  font-style: italic;
}

.export-progress-details small {
  display: block;
  max-width: 300px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}
</style>

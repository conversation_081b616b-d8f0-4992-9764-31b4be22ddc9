# Folder Note Navigation Crash Fix

## Files Modified
- `src/views/FoldersView.vue` - Fixed `openNoteInNotesView` function

## What Was Done
Fixed a critical bug where opening a note from a folder would crash the application. The issue was caused by using `window.location.href` for navigation instead of the Vue router.

## How It Was Fixed
Replaced the problematic navigation code in the `openNoteInNotesView` function:

### Before (Causing Crash):
```javascript
// Navigate to NotesView with the note ID as a query parameter
// This will allow NotesView to open this specific note
window.location.href = `/#/notes?noteId=${noteId}`;
```

### After (Fixed):
```javascript
// Navigate to NotesView with the note ID as a query parameter using Vue router
// This prevents application crashes by using proper Vue navigation
router.push({
  name: 'Notes',
  query: { noteId: noteId.toString() }
});
```

## Root Cause
The `window.location.href` approach causes a hard page reload, which disrupts Vue's component lifecycle and state management, leading to application crashes. This is particularly problematic in Electron applications where the entire app context can be lost.

## Solution Details
- Used Vue router's `push` method instead of direct URL manipulation
- Maintained the same query parameter structure (`noteId`) for compatibility
- Ensured proper string conversion of the note ID
- Preserved all existing functionality (recent items tracking, error handling)

## Impact
- Eliminates application crashes when opening notes from folders
- Maintains smooth navigation experience consistent with other parts of the app
- Preserves application state during navigation
- Follows Vue.js best practices for SPA navigation

## Testing Recommendations
1. Navigate to FoldersView
2. Open any folder containing notes
3. Click on a note to open it
4. Verify the application navigates to NotesView without crashing
5. Confirm the correct note is opened and displayed
6. Test with multiple notes to ensure consistency

## Related Code
This fix aligns the folder navigation behavior with the existing pattern used in BooksView, which correctly uses `router.push()` for note navigation.

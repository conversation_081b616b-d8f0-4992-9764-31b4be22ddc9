# NotesView State Persistence Implementation

## Files Modified
- `src/stores/notesViewStore.ts` (created)
- `src/views/NotesView.vue`
- `src/components/notes/NoteEditor.vue`

## What Was Done
Implemented state persistence for the Notes view to preserve user context when navigating between views. The implementation saves and restores:
- Selected note and folder
- Search query
- Multi-selection state
- Scroll positions (notes list)
- Editor cursor position and scroll position

## How It Was Implemented

### 1. Created NotesViewStore
A new Pinia store following the established pattern from `timerStore.ts`:
- Stores all relevant UI state
- Implements a 5-minute restoration window
- Provides methods for saving and retrieving state
- Uses computed property to determine if state should be restored

### 2. NotesView Integration
- Added store import and initialization
- Modified `loadNotes()` to check for restoration data before selecting first note
- Added watchers to save state changes (note selection, search, folder)
- Implemented scroll position tracking with debouncing (300ms)
- Added cleanup in `onBeforeUnmount` to remove event listeners

### 3. NoteEditor Integration
- Added cursor and scroll position tracking
- Implemented debounced state saving (500ms) on selection/content changes
- Modified note change handler to restore editor state when applicable
- Added proper cleanup to prevent memory leaks

### Key Features
- **Time-based restoration**: State expires after 5 minutes to prevent stale data
- **Graceful fallbacks**: If restoration fails, falls back to existing behavior
- **Non-breaking**: All existing functionality preserved
- **Performance optimized**: Debounced saves prevent excessive updates
- **Memory efficient**: No persistent storage, minimal memory footprint

## Testing Instructions
1. Open Notes view and select a note
2. Search for something specific
3. Scroll down in the notes list
4. Place cursor somewhere in the editor and type some text
5. Scroll down in the editor (if content is long enough)
6. Navigate to another view (e.g., Timer)
7. Return to Notes within 5 minutes
8. Verify that:
   - Your note is selected
   - Search query is preserved
   - Notes list scroll position is restored
   - Editor cursor position is restored
   - Editor scroll position is restored

## Debug Information
The implementation includes console logging to help debug issues:
- 🔄 When restoring state
- 💾 When saving editor state
- 📌 Cursor position changes
- 📜 Scroll position changes
- ✅/❌ Success/failure of restoration attempts

## Edge Cases Handled
- Note deleted while away (falls back to first note)
- Empty notes list
- Failed restoration (uses default behavior)
- Time expiry (after 5 minutes, starts fresh)
- Same note already selected (restoration still triggers)
- Multiple scroll containers (checks all possible containers)
- Editor not fully initialized (waits for proper timing)

## Known Limitations
- Cursor position may not restore perfectly if content has changed
- Scroll position depends on content being the same height
- Some editor extensions may interfere with cursor restoration
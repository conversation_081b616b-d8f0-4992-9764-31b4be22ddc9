# Windows Hidden Files Fix

## Files Modified
- `electron/main/api/sync-logic/file-operations.ts`

## What Was Done
Fixed an issue where files with dot prefixes (like `.cover.jpg`, `.sync-manifest.json`) were not appearing as hidden on Windows systems, despite having the dot prefix that makes them hidden on Unix/Linux systems.

## How It Was Fixed

### Problem Analysis
On Windows, simply prefixing a filename with a dot (`.`) does not make the file hidden. This is a Unix/Linux convention that doesn't work on Windows. On Windows, files need to have the "Hidden" attribute set using either:
1. The `attrib +h` command
2. Windows API calls to set file attributes

The sync system was creating files like `.cover.jpg` but they remained visible because the Windows hidden attribute wasn't being set.

### Solution Implementation

1. **Added Required Imports**:
   ```typescript
   import { spawn } from 'child_process';
   import { promisify } from 'util';
   ```

2. **Created Windows Hidden Attribute Function**:
   ```typescript
   private async setWindowsHiddenAttribute(filePath: string): Promise<void> {
     if (process.platform !== 'win32') {
       return; // Only works on Windows
     }

     try {
       await new Promise<void>((resolve, reject) => {
         const child = spawn('attrib', ['+h', filePath], {
           stdio: 'pipe',
           shell: true
         });

         child.on('close', (code) => {
           if (code === 0) {
             resolve();
           } else {
             reject(new Error(`attrib command failed with code ${code}`));
           }
         });

         child.on('error', (error) => {
           reject(error);
         });
       });
     } catch (error) {
       // Log warning but don't fail the operation
       console.warn(`[FileOperations] Failed to set hidden attribute for ${filePath}:`, error);
     }
   }
   ```

3. **Updated File Writing Methods**:
   - Modified `writeFileBuffer()` to call `setWindowsHiddenAttribute()` after writing files that start with a dot
   - Modified `writeFileAtomic()` to call `setWindowsHiddenAttribute()` after writing files that start with a dot

4. **Platform Detection**:
   - Added `process.platform === 'win32'` checks to only run the hidden attribute logic on Windows
   - The function gracefully does nothing on non-Windows platforms

### Technical Details

- **Command Used**: `attrib +h <filepath>` - Windows built-in command to set hidden attribute
- **Error Handling**: Failures to set hidden attribute are logged as warnings but don't fail the file operation
- **Cross-Platform**: Only runs on Windows (`process.platform === 'win32'`)
- **Performance**: Minimal overhead - only runs for dot-prefixed files on Windows

### Files Affected
This fix will properly hide the following sync-generated files on Windows:
- `.cover.jpg` (book cover images)
- `.sync-manifest.json` (sync metadata)
- Any other dot-prefixed files created by the sync system

## Testing
To test this fix:
1. Run a sync operation that creates cover images
2. Navigate to the sync directory in Windows Explorer
3. Verify that `.cover.jpg` files are now hidden (not visible unless "Show hidden files" is enabled)
4. Check that the files still function correctly for import/export operations

## Impact
- **User Experience**: Sync directories will now appear cleaner on Windows with hidden files properly hidden
- **Cross-Platform**: Maintains compatibility with Unix/Linux systems where dot-prefix hiding already works
- **Backward Compatibility**: Existing files will be hidden when next modified by the sync system

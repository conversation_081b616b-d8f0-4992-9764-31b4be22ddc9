<template>
  <Teleport to="body">
    <div class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ book.title }}</h3>
          <div class="header-buttons">
            <div class="close-button" @click="handleClose">
              <img src="/icons/close-icon.svg" alt="Close" />
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
          <button class="tab-button" :class="{ active: activeTab === 'details' }" @click="activeTab = 'details'">
            Details
          </button>
          <button class="tab-button" :class="{ active: activeTab === 'notes' }" @click="activeTab = 'notes'">
            Notes
          </button>
        </div>

        <!-- Details Tab -->
        <div v-if="activeTab === 'details'" class="tab-content">
          <div class="form-container">
            <div class="form-content">
              <div class="book-info-container">
                <!-- First row with cover and main info -->
                <div class="main-info-row">
                  <div class="cover-column">
                    <div class="cover-display" :class="{ 'editable': isEditMode }"
                      @click="isEditMode ? handleCoverUpload() : null">
                      <img
                        v-if="isEditMode ? (editData.cover_url || book.cover_media_url || book.cover_url) : (book.cover_media_url || book.cover_url)"
                        :src="isEditMode ? (editData.cover_url || book.cover_media_url || book.cover_url || '') : (book.cover_media_url || book.cover_url || '')"
                        alt="Book cover" class="cover-preview" />
                      <div v-else class="default-cover">
                        <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <rect width="32" height="40" rx="2" fill="#e0e0e0" />
                          <path d="M8 12h16v2H8v-2zm0 4h16v2H8v-2zm0 4h12v2H8v-2z" fill="#999" />
                        </svg>
                      </div>
                      <!-- Upload overlay for edit mode -->
                      <div v-if="isEditMode" class="cover-upload-overlay">
                        <div class="upload-text">
                          {{ coverUploadText }} </div>
                      </div>
                      <input ref="fileInput" type="file" accept="image/*" @change="handleFileChange"
                        style="display: none;" />
                    </div>
                  </div>

                  <div class="main-fields-column">
                    <!-- Title Field -->
                    <div class="form-field title-field" :class="{ 'edit-mode': isEditMode }">
                      <input v-if="isEditMode" type="text" v-model="editData.title" placeholder="Title"
                        class="title-input" />
                      <div v-else class="field-display title-display">{{ book.title }}</div>
                    </div>

                    <!-- Author Field -->
                    <div class="form-field author-field" :class="{ 'edit-mode': isEditMode }">
                      <input v-if="isEditMode" type="text" v-model="editData.author" placeholder="Author"
                        class="author-input" />
                      <div v-else class="field-display author-display">by {{ displayAuthor }}</div>
                    </div>

                    <hr class="section-divider">

                    <!-- Publication Year Field -->
                    <div class="form-field" :class="{ 'edit-mode': isEditMode }">
                      <label>Publication Year:</label>
                      <div class="field-content">
                        <input v-if="isEditMode" type="text" v-model="editData.publication_date"
                          @input="validatePublicationYear" :class="{ 'input-error': publicationYearError }"
                          placeholder="e.g., 2025 or -32 for BCE" />
                        <div v-else class="field-display">{{ formatPublicationYear(book.publication_date || null) }}</div>
                        <small v-if="isEditMode && publicationYearError" class="error-message">{{ publicationYearError
                        }}</small>
                      </div>
                    </div>

                    <!-- ISBN Field -->
                    <div class="form-field" :class="{ 'edit-mode': isEditMode }">
                      <label>ISBN:</label>
                      <div class="field-content">
                        <input v-if="isEditMode" type="text" v-model="editData.isbn" @input="validateISBN"
                          :class="{ 'input-error': isbnError }" placeholder="e.g., 978-3-16-148410-0" />
                        <div v-else class="field-display">{{ book.isbn || 'N/A' }}</div>
                        <small v-if="isEditMode && isbnError" class="error-message">{{ isbnError }}</small>
                      </div>
                    </div>

                    <!-- Genre Field -->
                    <div class="form-field" :class="{ 'edit-mode': isEditMode }">
                      <label>Genre:</label>
                      <div class="field-content">
                        <input v-if="isEditMode" type="text" v-model="editData.genres"
                          placeholder="e.g., Fiction, Mystery" />
                        <div v-else class="field-display wrappable-text">{{ book.genres || 'N/A' }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Second set of rows -->
                <div class="form-section">
                  <div class="form-row">
                    <!-- Language Field -->
                    <div class="form-field" :class="{ 'edit-mode': isEditMode }">
                      <label>Language:</label>
                      <div class="field-content">
                        <input v-if="isEditMode" type="text" v-model="editData.language" placeholder="e.g., English" />
                        <div v-else class="field-display">{{ displayLanguage }}</div>
                      </div>
                    </div>

                    <!-- Pages Field -->
                    <div class="form-field" :class="{ 'edit-mode': isEditMode }">
                      <label>Pages:</label>
                      <div class="field-content">
                        <input v-if="isEditMode" type="number" min="0" v-model="editData.page_count"
                          @input="validatePageCount" :class="{ 'input-error': pageCountError }"
                          placeholder="e.g., 320" />
                        <div v-else class="field-display">{{ book.page_count || 'N/A' }}</div>
                        <small v-if="isEditMode && pageCountError" class="error-message">{{ pageCountError }}</small>
                      </div>
                    </div>
                  </div>

                  <div class="form-row">
                    <div class="form-field">
                      <label>Notes:</label>
                      <div class="field-content">
                        <div class="field-display">{{ book.notesCount || 0 }}</div>
                      </div>
                    </div>

                    <div class="form-field">
                      <label>Date added:</label>
                      <div class="field-content">
                        <div class="field-display">{{ formatDate(book.created_at) }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- Rating Field -->
                  <div class="form-field">
                    <label>Your rating:</label>
                    <div class="field-content">
                      <div class="rating-container">
                        <div class="rating-stars">
                          <img v-for="star in 5" :key="star" @click="setRating(star)"
                            :src="star <= currentRating ? starFilledIcon : starIcon"
                            class="star" :class="{ 'star-filled': star <= currentRating }" width="16" height="16"
                            alt="Star rating" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Description Field -->
                  <div class="form-field full-width description-field" :class="{ 'edit-mode': isEditMode }">
                    <label>Description:</label>
                    <div class="field-content">
                      <textarea v-if="isEditMode" v-model="editData.description"
                        placeholder="Enter book description..."></textarea>
                      <div v-else class="field-display description">{{ book.description || 'No description available' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Notes Tab -->
        <div v-if="activeTab === 'notes'" class="tab-content notes-tab">
          <div class="notes-header">
            <div class="search-container">
              <img src="/icons/search-icon.svg" alt="Search" class="search-icon" />
              <input type="text" v-model="searchQuery" placeholder="Search Note" class="search-input" />
            </div>
            <button class="new-note-button" @click="createNewNote">
              <img src="/icons/plus-icon.svg" alt="New note" />
              New Note
            </button>
          </div>

          <div class="notes-container">
            <div class="notes-content">
              <div class="notes-list">
                <div v-if="filteredNotes.length === 0" class="no-notes-container">
                  <p v-if="searchQuery" class="no-notes-message">No notes found matching "{{ searchQuery }}"</p>
                  <div v-else-if="filteredNotes.length === 0" class="empty-notes-state">
                    <p>No notes found for this book.</p>
                    <p>Click "New Note" to create your first note!</p>
                  </div>
                </div>

                <div v-for="note in filteredNotes" :key="note.id" class="note-card" @click="openNote(note)">
                  <div class="note-card-content">
                    <h3 class="note-title">{{ note.title }}</h3>
                    <p class="note-excerpt">{{ getPreviewText(note.content) }}</p>
                    <div class="note-meta">
                      <span class="note-date">{{ formatDate(note.updated_at) }}</span>
                      <span class="note-path">{{ getNotePath(note) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
          <button class="btn btn-danger" v-if="activeTab === 'details' && !isEditMode" @click="deleteBook">Delete
            Book</button>
          <div class="spacer"></div>
          <button v-if="isEditMode && activeTab === 'details'" class="btn btn-secondary" @click="cancelEdit"
            style="margin-right: 8px;">Cancel</button>
          <button v-if="!isEditMode" class="btn btn-secondary" @click="handleClose">Close</button>
          <button v-if="activeTab === 'details'" class="btn btn-primary" @click="toggleEditMode"
            style="margin-left: 8px;">{{ isEditMode ? 'Save' : 'Edit' }}</button>
        </div>
      </div>
    </div>

    <!-- Delete Book Modal -->
    <div v-if="showDeleteModal" class="modal-overlay delete-modal-overlay">
      <DeleteBookModal :bookTitle="book.title" :bookId="book.id || 0" @close="showDeleteModal = false"
        @cancel="showDeleteModal = false" @delete="confirmDeleteBook" />
    </div>
  </Teleport>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, computed, onMounted, watch } from 'vue'
import type { BookWithNoteCount, Note, Folder } from '../../types/electron-api'
import DeleteBookModal from './DeleteBookModal.vue'
import { convertLanguageCode, getPrimaryLanguage } from '../../utils/language-converter'

// Import icons as modules for proper path resolution in production
import starIcon from '/icons/star-icon.svg'
import starFilledIcon from '/icons/star-filled-icon.svg'

export default defineComponent({
  name: 'BookDetailsModal',
  components: {
    DeleteBookModal
  },
  props: {
    book: {
      type: Object as PropType<BookWithNoteCount>,
      required: true
    }
  },
  emits: ['close', 'update-book', 'open-note', 'create-note', 'delete-book'],
  setup(props, { emit }) {
    const activeTab = ref<'details' | 'notes'>('details')
    const isEditMode = ref(false)
    const searchQuery = ref('')
    const notes = ref<Note[]>([])
    const folders = ref<Folder[]>([])
    const currentRating = ref(props.book.rating || 0)

    // Validation variables
    const currentYear = new Date().getFullYear()
    const publicationYearError = ref<string>('')
    const pageCountError = ref<string>('')
    const isbnError = ref<string>('')

    // Helper function to get fresh data for editing
    const getFreshEditData = () => ({
      title: props.book.title,
      author: props.book.author || '',
      isbn: props.book.isbn || '',
      publication_date: props.book.publication_date || '',
      language: props.book.language || '',
      genres: props.book.genres || '',
      page_count: props.book.page_count || null,
      description: props.book.description || '',
      cover_url: props.book.cover_media_url || props.book.cover_url // Preserve existing cover
    });

    const editData = ref(getFreshEditData());

    // Computed property to display author with truncation for multiple authors
    const displayAuthor = computed(() => {
      const author = props.book.author;
      if (!author) return 'Unknown Author';

      const authors = author.split(',').map(a => a.trim());
      if (authors.length > 1) {
        return `${authors[0]}, ...`;
      }
      return author;
    });

    // Computed property to display language in human-readable format
    const displayLanguage = computed(() => {
      return getPrimaryLanguage(props.book.language);
    });    // Fixed validation functions with consistent data types
    const validatePublicationYear = () => {
      // Clear error first
      publicationYearError.value = '';

      // Allow empty value
      if (!editData.value.publication_date) {
        return;
      }

      // Check if it's a valid number
      const yearValue = String(editData.value.publication_date).trim();
      const yearNum = Number(yearValue);

      // Make sure it's a valid integer year
      if (isNaN(yearNum) || !Number.isInteger(yearNum)) {
        publicationYearError.value = 'Please enter a valid year';
        return;
      }

      // Make sure it's within a reasonable range - up to current year
      if (yearNum > currentYear) {
        publicationYearError.value = `Year cannot be greater than ${currentYear}`;
        return;
      }

      // Year 0 doesn't exist in historical calendar (1 BCE was followed by 1 CE)
      if (yearNum === 0) {
        publicationYearError.value = 'Year 0 does not exist in the calendar (1 BCE was followed by 1 CE)';
        return;
      }

      // Valid year - update the form data with the parsed number as string (consistent with database schema)
      editData.value.publication_date = yearNum.toString();
    };

    const validatePageCount = () => {
      // Clear error first
      pageCountError.value = '';

      // Allow empty value (consistent with AddBookManuallyModal)
      if (!editData.value.page_count) {
        return;
      }

      // Check if it's a valid number
      const pageCount = Number(editData.value.page_count);

      // Make sure it's a non-negative value
      if (pageCount < 0) {
        pageCountError.value = 'Page count cannot be negative';
        return;
      }

      // Make sure it's an integer
      if (!Number.isInteger(pageCount)) {
        pageCountError.value = 'Please enter a valid page count';
        return;
      }

      // Valid page count - update the edit data
      editData.value.page_count = pageCount;
    };

    const validateISBN = () => {
      // Clear error first
      isbnError.value = '';

      // Allow empty value
      if (!editData.value.isbn || editData.value.isbn.trim() === '') {
        return;
      }

      // Remove any hyphens or spaces
      const cleanedISBN = editData.value.isbn.replace(/[-\s]/g, '');

      // Check if it contains only numbers
      if (!/^\d+$/.test(cleanedISBN)) {
        isbnError.value = 'ISBN can only contain numbers, hyphens, and spaces';
        return;
      }

      // Check if it's 10 or 13 digits
      if (cleanedISBN.length !== 10 && cleanedISBN.length !== 13) {
        isbnError.value = 'ISBN must be 10 or 13 digits';
        return;
      }

      // Valid ISBN - update the edit data with the cleaned value
      editData.value.isbn = cleanedISBN;
    };

    const coverUploadText = computed(() => {
      const hasCover = editData.value.cover_url || props.book.cover_media_url || props.book.cover_url;
      return hasCover ? 'Change Cover' : 'Upload Cover';
    });

    const filteredNotes = computed(() => {
      if (!searchQuery.value) return notes.value

      const query = searchQuery.value.toLowerCase()
      return notes.value.filter(note =>
        note.title.toLowerCase().includes(query) ||
        (note.content && note.content.toLowerCase().includes(query))
      )
    })

    const loadNotes = async () => {
      if (props.book.id) {
        try {
          const allNotes = await window.db.notes.getAll()
          notes.value = allNotes.filter(note => note.book_id === props.book.id)
        } catch (error) {
          console.error('Failed to load notes:', error)
        }
      }
    }

    const loadFolders = async () => {
      try {
        const allFolders = await window.db.folders.getAll()
        folders.value = allFolders
      } catch (error) {
        console.error('Failed to load folders:', error)
      }
    };

    const cancelEdit = () => {
      // Reset editData to current book props, discarding any changes
      editData.value = getFreshEditData();
      currentRating.value = props.book.rating || 0;
      // Clear all validation errors
      publicationYearError.value = '';
      isbnError.value = '';
      pageCountError.value = '';
      isEditMode.value = false;
    };

    const handleClose = () => {
      if (isEditMode.value) {
        cancelEdit()
      }
      emit('close')
    };

    // Fixed toggleEditMode with improved validation timing
    const toggleEditMode = async () => {
      if (isEditMode.value) { // Currently in edit mode, about to save and switch to view mode
        // Reset all validation errors first
        publicationYearError.value = '';
        isbnError.value = '';
        pageCountError.value = '';

        // Validate fields before saving - always call, let functions handle empty values
        validatePublicationYear();
        validateISBN();
        validatePageCount();

        // Only save if there are no validation errors
        if (!publicationYearError.value && !isbnError.value && !pageCountError.value) {
          await saveChanges();
          isEditMode.value = false;
        }
        // If there are validation errors, stay in edit mode
      } else { // Currently in view mode, about to switch to edit mode
        // Reset editData to current book props before entering edit mode
        editData.value = getFreshEditData();
        currentRating.value = props.book.rating || 0;
        isEditMode.value = true;
      }
    };

    const saveChanges = async () => {
      if (!props.book.id) return;

      try {
        let updatedBook = {
          ...editData.value,
          rating: currentRating.value,
          // Ensure publication_date is properly formatted for database
          publication_date: editData.value.publication_date ? editData.value.publication_date.toString() : null
        };

        // Handle cover logic carefully to prevent accidental deletion
        let coverUpdated = false;

        if (editData.value.cover_url && typeof editData.value.cover_url === 'string' && editData.value.cover_url.startsWith('data:')) {
          // New cover was uploaded - remove from book update and handle separately
          updatedBook.cover_url = null;

          try {
            // Convert data URL to buffer and save as book cover
            const response = await fetch(editData.value.cover_url);
            const blob = await response.blob();
            const arrayBuffer = await blob.arrayBuffer();
            const buffer = new Uint8Array(arrayBuffer);

            // Save the cover using the media API (now supports Uint8Array directly)
            await window.db.media.saveBookCover(props.book.id, buffer, 'cover.jpg');
            coverUpdated = true;
            console.log('Cover successfully updated for book:', props.book.id);
          } catch (coverError) {
            console.error('Failed to save cover:', coverError);
          }
        } else {
          // No new cover uploaded - preserve existing cover_url to prevent deletion
          const bookUpdateFields = {
            title: updatedBook.title,
            author: updatedBook.author,
            isbn: updatedBook.isbn,
            publication_date: updatedBook.publication_date,
            language: updatedBook.language,
            genres: updatedBook.genres,
            page_count: updatedBook.page_count,
            description: updatedBook.description,
            rating: updatedBook.rating,
            cover_url: updatedBook.cover_url
          };
          updatedBook = bookUpdateFields;
        }

        await window.db.books.update(props.book.id, updatedBook)

        // Reset cover_url in editData after successful save
        if (coverUpdated) {
          editData.value.cover_url = null;
        }

        // Emit update with a flag indicating if cover was updated
        emit('update-book', { ...props.book, ...updatedBook, coverUpdated });
      } catch (error) {
        console.error('Failed to update book:', error);
      }
    };

    const setRating = async (rating: number) => {
      currentRating.value = (currentRating.value === rating) ? 0 : rating;
      if (!isEditMode.value) {
        if (!props.book.id) {
          console.error('Book ID is missing, cannot save rating.');
          return;
        }
        try {
          await window.db.books.update(props.book.id, { rating: currentRating.value });
          emit('update-book', { ...props.book, rating: currentRating.value });
        } catch (error) {
          console.error('Failed to update rating:', error);
        }
      }
    };

    const formatDate = (dateString?: string) => {
      if (!dateString) return 'N/A'
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        })
      } catch {
        return 'N/A'
      }
    };    // Format publication date to show year with BCE/CE notation as appropriate
    const formatPublicationYear = (date: string | null): string => {
      if (!date) return 'N/A';

      // If it's already a number (positive or negative), format it directly
      const yearNum = Number(date);
      if (!isNaN(yearNum) && Number.isInteger(yearNum)) {
        // For BCE years (negative numbers)
        if (yearNum < 0) {
          return `${Math.abs(yearNum)} BCE`;
        }
        // For CE years (positive numbers)
        else {
          return `${yearNum} CE`;
        }
      }

      // Extract modern year pattern if it exists
      const yearMatch = date.match(/\b(19|20)\d{2}\b/);
      if (yearMatch && yearMatch[0]) {
        return `${yearMatch[0]} CE`;
      }

      // Try parsing as a date if no pattern match
      try {
        const dateObj = new Date(date);
        if (!isNaN(dateObj.getTime())) {
          return `${dateObj.getFullYear()} CE`;
        }
      } catch (e) {
        // Parsing failed, return original
      }

      return date;
    };

    const getPreviewText = (content?: string) => {
      if (!content) return ''
      const plainText = content.replace(/<[^>]*>/g, '').trim()
      return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
    }

    const getNotePath = (note: Note) => {
      if (!note.folder_id) return 'Uncategorized'

      const folder = folders.value.find(f => f.id === note.folder_id)
      if (!folder) return 'Unknown'

      const pathFolders: string[] = [folder.name]
      let currentFolder = folder

      while (currentFolder.parent_id) {
        const parentFolder = folders.value.find(f => f.id === currentFolder.parent_id)
        if (!parentFolder) break
        pathFolders.unshift(parentFolder.name)
        currentFolder = parentFolder
      }

      return pathFolders.join('/')
    }

    const showDeleteModal = ref(false);
    const fileInput = ref<HTMLInputElement>();

    const deleteBook = () => {
      showDeleteModal.value = true;
    }

    const confirmDeleteBook = async () => {
      if (!props.book.id) return

      try {
        await window.db.books.delete(props.book.id)
        emit('delete-book', props.book.id)
        emit('close')
      } catch (error) {
        console.error('Failed to delete book:', error)
      }
    }

    const createNewNote = () => {
      if (!props.book.id) {
        console.error('Cannot create note: book ID is missing')
        return
      }
      emit('create-note', props.book.id)
    }

    const openNote = (note: Note) => {
      emit('open-note', note)
    }

    const handleCoverUpload = () => {
      fileInput.value?.click();
    };

    const handleFileChange = (event: Event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            editData.value.cover_url = e.target.result as string;
          }
        };
        reader.readAsDataURL(file);
      }
    };

    // Watch for external changes to the book prop
    watch(() => props.book, (newBookProps) => {
      if (!isEditMode.value) {
        editData.value = {
          title: newBookProps.title,
          author: newBookProps.author || '',
          isbn: newBookProps.isbn || '',
          publication_date: newBookProps.publication_date || '',
          language: newBookProps.language || '',
          genres: newBookProps.genres || '',
          page_count: newBookProps.page_count || null,
          description: newBookProps.description || '',
          cover_url: newBookProps.cover_media_url || newBookProps.cover_url // Preserve existing cover
        };
        currentRating.value = newBookProps.rating || 0;
      }
    }, { deep: true });

    // Watch for tab changes to load notes when switching to notes tab
    // and to exit edit mode when switching away from details tab
    watch(activeTab, (newTab, oldTab) => {
      // If we're leaving the details tab while in edit mode, cancel the edit
      if (oldTab === 'details' && isEditMode.value) {
        cancelEdit();
      }

      // Load notes and folders when switching to notes tab
      if (newTab === 'notes') {
        loadNotes();
        loadFolders();
      }
    });

    onMounted(() => {
      if (activeTab.value === 'notes') {
        loadNotes();
        loadFolders();
      }
    });

    return {
      activeTab,
      isEditMode,
      searchQuery,
      notes,
      folders,
      filteredNotes,
      currentRating,
      editData,
      displayAuthor,
      displayLanguage,
      publicationYearError,
      pageCountError,
      isbnError,
      validatePublicationYear,
      validatePageCount,
      validateISBN,
      handleClose,
      coverUploadText,
      cancelEdit,
      toggleEditMode,
      setRating,
      formatDate,
      formatPublicationYear,
      getPreviewText,
      getNotePath,
      deleteBook,
      confirmDeleteBook,
      showDeleteModal,
      createNewNote,
      openNote,
      fileInput,
      handleCoverUpload,
      handleFileChange,
      // Icon imports
      starIcon,
      starFilledIcon
    }
  }
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  isolation: isolate;
  padding: 20px;
  box-sizing: border-box;
}

.modal-content {
  background-color: var(--color-modal-bg);
  border-radius: 16px;
  box-shadow: 0 8px 24px var(--color-card-shadow);
  width: 650px;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Montserrat', sans-serif;
  overflow: hidden;
  max-height: calc(100vh - 40px);
  min-height: 300px;
}

.modal-header {
  padding: 16px 40px;
  border-bottom: 1px solid var(--color-modal-border);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  text-align: center;
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-buttons {
  position: absolute;
  top: 24px;
  right: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.close-button img {
  width: 24px;
  height: 24px;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--color-modal-border);
  background-color: var(--color-bg-secondary);
  flex-shrink: 0;
}

.tab-button {
  flex: 1;
  padding: 16px;
  background: none;
  border: none;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab-button.active {
  color: var(--color-text-primary);
  background-color: var(--color-modal-bg);
  border-bottom-color: var(--color-primary);
}

.tab-button:hover:not(.active) {
  background-color: var(--color-nav-item-hover);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
  scrollbar-gutter: stable;
}

.tab-content::-webkit-scrollbar {
  width: 8px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.tab-content.notes-tab {
  overflow-y: auto;
}

/* Details Tab Styles */
.form-container {
  padding: 16px;
  flex: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.form-content {
  border: 1px solid var(--color-card-border);
  border-radius: 10px;
  padding: 15px;
  position: relative;
  background-color: var(--color-bg-tertiary);
  width: 550px;
  margin: 0 auto;
  box-sizing: border-box;
}

.book-info-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
  margin-bottom: 0;
}

.main-info-row {
  display: flex;
  gap: 20px;
  width: 100%;
  align-items: stretch;
}

.cover-column {
  width: 135px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.main-fields-column {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  margin-top: 15px;
}

.cover-display {
  width: 135px;
  height: 192px;
  background-color: var(--color-bg-tertiary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cover-display.editable {
  cursor: pointer;
  transition: all 0.2s;
}

.cover-display.editable:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px var(--color-card-shadow);
}

.cover-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
  z-index: 1;
}

.cover-display.editable:hover .cover-upload-overlay {
  opacity: 1;
}

.upload-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  padding: 8px;
  line-height: 1.2;
}

.cover-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: var(--color-bg-tertiary);
}

/* Fixed form field styles for better alignment */
.form-field {
  display: flex;
  flex-direction: row;
  align-items: center;
  /* Changed from flex-start to center for better alignment */
  flex: 1;
  min-width: 0;
  gap: 10px;
  min-height: 40px;
  /* Added consistent minimum height */
}

/* Edit mode makes fields vertical */
.form-field.edit-mode {
  flex-direction: column;
  align-items: stretch;
  gap: 3px;
  min-height: auto;
  /* Reset min-height for edit mode */
}

.form-field.full-width {
  width: 100%;
}

/* Special styling for title and author fields */
.form-field.title-field,
.form-field.author-field {
  flex-direction: column;
  align-items: stretch;
  gap: 3px;
  min-height: auto;
  /* Reset min-height for these special fields */
}

.form-field>label {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: 500;
  min-width: 130px;
  text-align: left;
  flex-shrink: 0;
  line-height: 1.2;
  /* Added for consistent line height */
}

/* In edit mode, labels are full width */
.form-field.edit-mode>label {
  min-width: auto;
  width: 100%;
  margin-bottom: 3px;
}

.form-field.description-field {
  flex-direction: column;
  align-items: stretch;
  min-height: auto;
  /* Reset min-height for description field */
}

.form-field.description-field>label {
  width: auto;
  min-width: 0;
  margin-bottom: 6px;
}

/* Field display styles - Fixed alignment and overflow */
.field-display {
  font-size: 14px;
  color: var(--color-text-primary);
  min-height: 32px;
  box-sizing: border-box;
  padding: 6px 0;
  flex: 1;
  background-color: transparent;
  border: none;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  line-height: 1.2;
  /* Added for consistent line height */
  max-width: 100%;
  /* Added to prevent overflow */
}

.field-display.title-display {
  font-size: 18px;
  font-weight: 600;
  min-height: 36px;
}

.field-display.author-display {
  font-size: 16px;
  min-height: 34px;
  color: var(--color-text-secondary);
}

/* Fixed wrappable text to handle overflow properly */
.field-display.wrappable-text {
  white-space: normal;
  overflow: hidden;
  /* Changed from visible to hidden */
  text-overflow: clip;
  min-height: 32px;
  line-height: 1.4;
  align-items: flex-start;
  padding-top: 8px;
  word-wrap: break-word;
  /* Added to break long words */
  word-break: break-word;
  /* Added for better word breaking */
  max-width: 100%;
  /* Ensure it doesn't exceed container width */
}

/* Field content wrapper - Added max-width constraint */
.field-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  max-width: 100%;
  /* Added to prevent overflow */
  overflow: hidden;
  /* Added to contain overflow */
}

/* Input styling */
.form-field input,
.form-field textarea {
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  color: var(--color-input-text);
  outline: none;
  transition: all 0.2s;
  background-color: var(--color-input-bg);
  width: 100%;
  box-sizing: border-box;
  height: 32px;
}

/* Special styling for title and author inputs */
.title-input {
  font-size: 18px;
  font-weight: 600;
  height: 36px;
}

.author-input {
  font-size: 16px;
  height: 34px;
}

.form-field input:focus,
.form-field textarea:focus {
  border-color: var(--color-input-focus);
  box-shadow: 0 0 0 2px rgba(74, 74, 74, 0.1);
}

.field-display.description {
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 8px 12px;
  background-color: var(--color-bg-secondary);
  height: 130px;
  min-height: 130px;
  max-height: 130px;
  overflow-y: auto;
  line-height: 1.4;
  width: 100%;
  box-sizing: border-box;
  white-space: normal;
  text-overflow: clip;
  align-items: flex-start;
}

.field-display.description::-webkit-scrollbar {
  width: 8px;
}

.field-display.description::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.field-display.description::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.field-display.description::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

/* Textarea styling */
.form-field.description-field textarea {
  resize: none;
  height: 130px;
  min-height: 130px;
  line-height: 1.4;
  padding: 8px 12px;
  overflow-y: auto;
}

.form-field.description-field textarea::-webkit-scrollbar {
  width: 8px;
}

.form-field.description-field textarea::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.form-field.description-field textarea::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.form-field.description-field textarea::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.form-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.rating-container {
  display: flex;
  align-items: center;
}

.rating-stars {
  display: flex;
  gap: 12px;
  padding: 2px 0;
}

.star {
  cursor: pointer;
  transition: all 0.2s;
  width: 20px;
  height: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.star:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
}

/* Error styling */
.input-error {
  border-color: var(--color-error) !important;
  background-color: rgba(229, 57, 53, 0.05) !important;
}

.error-message {
  color: var(--color-error);
  font-size: 12px;
  margin-top: 4px;
  display: block;
  width: 100%;
}

.section-divider {
  width: 100%;
  border: none;
  border-top: 1px solid var(--color-border-primary);
  margin: 12px 0;
}

/* Notes Tab Styles */
.notes-tab {
  display: flex;
  flex-direction: column;
}

.notes-header {
  padding: 16px 0 10px 0;
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
  width: 550px;
  margin: 0 auto;
  box-sizing: border-box;
}

.search-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  opacity: 0.5;
  top: 50%;
  transform: translateY(-50%);
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 12px 0 36px;
  border: 1px solid var(--color-input-border);
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  outline: none;
  transition: border-color 0.2s;
  box-sizing: border-box;
  background-color: var(--color-input-bg);
  color: var(--color-input-text);
}

.search-input:focus {
  border-color: var(--color-input-focus);
}

.new-note-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 12px;
  height: 40px;
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
  box-sizing: border-box;
}

.new-note-button:hover {
  background-color: var(--color-btn-secondary-hover);
}

.new-note-button img {
  width: 14px;
  height: 14px;
}

.notes-container {
  padding: 0 16px 16px 16px;
  flex: 1;
  box-sizing: border-box;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 500px;
}

.notes-content {
  border: 1px solid var(--color-border-primary);
  border-radius: 10px;
  padding: 10px;
  position: relative;
  background-color: var(--color-bg-secondary);
  width: 550px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  scrollbar-gutter: stable;
}

.notes-content::-webkit-scrollbar {
  width: 8px;
}

.notes-content::-webkit-scrollbar-track {
  background: var(--color-scrollbar-track);
}

.notes-content::-webkit-scrollbar-thumb {
  background: var(--color-scrollbar-thumb);
  border-radius: 4px;
}

.notes-content::-webkit-scrollbar-thumb:hover {
  background: var(--color-scrollbar-thumb-hover);
}

.notes-list {
  flex: 1;
  padding: 5px;
}

.no-notes-container {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 0;
}

.no-notes-message {
  margin-bottom: 15px;
}

.empty-notes-state {
  text-align: center;
  color: var(--color-text-secondary);
  padding: 40px 20px;
}

.empty-notes-state p {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.empty-notes-state p:last-child {
  margin-bottom: 0;
  font-weight: 500;
  color: var(--color-text-primary);
}

/* Note Card Styling */
.note-card {
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease, border-color 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  position: relative;
  user-select: none;
  background-color: var(--color-card-bg);
  margin-bottom: 12px;
  border-radius: 8px;
  border: 1px solid var(--color-card-border);
  box-shadow: 0 1px 3px var(--color-card-shadow);
}

.note-card:first-child {
  margin-top: 0;
}

.note-card:last-child {
  margin-bottom: 0;
}

.note-card:hover {
  background-color: var(--color-nav-item-hover);
  border-color: var(--color-border-hover);
  box-shadow: 0 2px 8px var(--color-card-hover-shadow);
}

.note-card-content {
  width: 100%;
  flex: 1;
  padding-left: 0;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-family: 'Montserrat', sans-serif;
}

.note-excerpt {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  line-height: 1.4;
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--color-text-muted);
  width: 100%;
  font-family: 'Montserrat', sans-serif;
}

.note-date {
  font-weight: 500;
  color: var(--color-text-tertiary);
}

.note-path {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  color: var(--color-text-muted);
  font-style: italic;
}

.modal-footer {
  padding: 16px 40px;
  border-top: 1px solid var(--color-modal-border);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-shrink: 0;
  background-color: var(--color-modal-bg);
}

.btn {
  padding: 10px 24px;
  min-width: 130px;
  border-radius: 10px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
}

.btn-secondary {
  background-color: var(--color-btn-secondary-bg);
  color: var(--color-btn-secondary-text);
  border: 1px solid var(--color-btn-secondary-border);
}

.btn-secondary:hover {
  background-color: var(--color-btn-secondary-hover);
}

.btn-primary {
  background-color: var(--color-btn-primary-bg);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-btn-primary-bg);
}

.btn-primary:hover {
  background-color: var(--color-btn-primary-hover);
  border-color: var(--color-btn-primary-hover);
}

.btn-danger {
  background-color: var(--color-error);
  color: var(--color-btn-primary-text);
  border: 1px solid var(--color-error);
}

.btn-danger:hover {
  background-color: var(--color-error);
  border-color: var(--color-error);
  opacity: 0.8;
}

.spacer {
  flex-grow: 1;
}

@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    max-height: calc(100vh - 20px);
  }

  .modal-header {
    padding: 12px 20px;
  }

  .modal-header h3 {
    font-size: 24px;
    max-width: 250px;
  }

  .header-buttons {
    top: 16px;
    right: 16px;
  }

  .modal-footer {
    padding: 12px 20px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .btn {
    min-width: 100px;
    padding: 8px 16px;
    font-size: 14px;
  }

  .form-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-header {
    width: calc(100vw - 72px);
    max-width: 550px;
  }
}

@media (max-width: 640px) {
  .main-info-row {
    flex-direction: column;
    gap: 16px;
  }

  .cover-column {
    width: 100%;
  }

  .cover-display {
    width: 100%;
    height: 250px;
    margin: 0 auto;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .notes-header {
    flex-direction: column;
    gap: 12px;
  }

  .new-note-button {
    width: 100%;
    justify-content: center;
  }

  .modal-footer {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .spacer {
    display: none;
  }

  .form-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-content {
    width: calc(100vw - 72px);
    max-width: 550px;
  }

  .notes-header {
    width: calc(100vw - 72px);
    max-width: 550px;
  }
}

@media (max-height: 600px) {
  .modal-content {
    max-height: calc(100vh - 20px);
  }

  .form-container {
    padding: 12px;
  }

  .notes-container {
    padding: 12px;
  }

  .cover-display {
    height: 150px;
  }

  .form-field.description-field textarea,
  .field-display.description {
    height: 80px;
    min-height: 80px;
    max-height: 80px;
  }
}

/* Title wrapping override - Allow long titles to wrap to multiple lines */
.field-display.title-display {
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  align-items: flex-start !important;
  line-height: 1.3 !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  padding-top: 8px !important;
}
</style>
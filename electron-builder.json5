{
  $schema: "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  appId: "com.noti.app",
  asar: true,
  productName: "Noti",
  directories: {
    output: "build-output/${version}",
    buildResources: "build",
  },
  files: ["dist", "dist-electron", "node_modules/sqlite3/**/*"],
  npmRebuild: false,
  buildDependenciesFromSource: false,
  compression: "normal",
  removePackageScripts: true,
  nodeGypRebuild: false,
  asarUnpack: ["node_modules/sqlite3/**/*"],
  extraMetadata: {
    main: "dist-electron/main/index.js",
  },
  mac: {
    target: [
      {
        target: "dmg",
        arch: ["x64", "arm64"],
      },
    ],
    artifactName: "${productName}-${version}-${arch}.${ext}",
    category: "public.app-category.productivity",
    icon: "build/icon.icns",
  },
  win: {
    target: [
      {
        target: "nsis",
        arch: ["x64"],
      },
    ],
    artifactName: "${productName}-Setup-${version}-${arch}.${ext}",
    icon: "build/icon.ico",
    sign: null,
    extraFiles: [
      {
        from: "node_modules/sqlite3/lib/binding",
        to: "resources/app.asar.unpacked/node_modules/sqlite3/lib/binding",
        filter: ["**/*"],
      },
    ],
  },
  linux: {
    target: [
      {
        target: "AppImage",
        arch: ["x64"],
      },
    ],
    artifactName: "${productName}-${version}-${arch}.${ext}",
    category: "Office",
    icon: "build",
    executableName: "noti",
    desktop: {
      Name: "Noti",
      Terminal: false,
      Type: "Application",
      Icon: "noti",
      StartupWMClass: "Noti",
      Categories: "Office;Utility;",
    },
  },
  nsis: {
    oneClick: false,
    perMachine: false,
    allowToChangeInstallationDirectory: true,
    deleteAppDataOnUninstall: false,
    installerIcon: "build/icon.ico",
    uninstallerIcon: "build/icon.ico",
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "Noti",
  },
  dmg: {
    contents: [
      {
        x: 410,
        y: 150,
        type: "link",
        path: "/Applications",
      },
      {
        x: 130,
        y: 150,
        type: "file",
      },
    ],
  },
}
